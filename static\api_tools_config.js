// API工具配置管理JavaScript

let currentEditingId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadTools();
});

// 显示添加表单
function showAddForm() {
    currentEditingId = null;
    document.getElementById('formTitle').textContent = '添加API工具';
    document.getElementById('toolId').value = '';
    document.getElementById('apiToolForm').reset();
    document.getElementById('isActive').checked = true;
    clearParams();
    document.getElementById('toolForm').classList.add('active');
    document.getElementById('toolForm').scrollIntoView({ behavior: 'smooth' });
}

// 显示编辑表单
function showEditForm(toolId) {
    currentEditingId = toolId;
    document.getElementById('formTitle').textContent = '编辑API工具';
    
    // 获取工具详情
    fetch(`/api/tools/${toolId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const tool = data.tool;
                document.getElementById('toolId').value = tool.id;
                document.getElementById('toolNameCn').value = tool.tool_name_cn;
                document.getElementById('toolNameEn').value = tool.tool_name_en;
                document.getElementById('description').value = tool.description || '';
                document.getElementById('servicePath').value = tool.service_path;
                document.getElementById('postPath').value = tool.post_path;
                document.getElementById('keywords').value = tool.keywords;
                document.getElementById('isActive').checked = tool.is_active;
                
                // 加载参数配置
                loadParams('requestParams', tool.request_params || {});
                loadParams('responseParams', tool.response_params || {});
                
                document.getElementById('toolForm').classList.add('active');
                document.getElementById('toolForm').scrollIntoView({ behavior: 'smooth' });
            } else {
                showAlert('获取工具详情失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('获取工具详情失败: ' + error.message, 'error');
        });
}

// 隐藏表单
function hideForm() {
    document.getElementById('toolForm').classList.remove('active');
    currentEditingId = null;
}

// 加载工具列表
function loadTools() {
    const toolsList = document.getElementById('toolsList');
    toolsList.innerHTML = '<div class="loading">正在加载工具列表...</div>';
    
    fetch('/api/tools')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderToolsList(data.tools);
            } else {
                toolsList.innerHTML = '<div class="loading">加载失败: ' + data.message + '</div>';
            }
        })
        .catch(error => {
            toolsList.innerHTML = '<div class="loading">加载失败: ' + error.message + '</div>';
        });
}

// 渲染工具列表
function renderToolsList(tools) {
    const toolsList = document.getElementById('toolsList');
    
    if (tools.length === 0) {
        toolsList.innerHTML = '<div class="loading">暂无工具配置</div>';
        return;
    }
    
    let html = '';
    tools.forEach(tool => {
        const statusClass = tool.is_active ? 'status-active' : 'status-inactive';
        const statusText = tool.is_active ? '启用' : '禁用';
        
        html += `
            <div class="tool-item">
                <div class="tool-info">
                    <h3>${tool.tool_name_cn} (${tool.tool_name_en})</h3>
                    <p><strong>描述:</strong> ${tool.description || '无描述'}</p>
                    <p><strong>服务段:</strong> ${tool.service_path}</p>
                    <p><strong>POST路径:</strong> ${tool.post_path}</p>
                    <p><strong>关键词:</strong> ${tool.keywords}</p>
                    <p><strong>状态:</strong> <span class="status-badge ${statusClass}">${statusText}</span></p>
                </div>
                <div class="tool-actions">
                    <button class="btn btn-primary" onclick="showEditForm(${tool.id})">编辑</button>
                    <button class="btn btn-danger" onclick="deleteTool(${tool.id}, '${tool.tool_name_cn}')">删除</button>
                </div>
            </div>
        `;
    });
    
    toolsList.innerHTML = html;
}

// 提交表单
document.getElementById('apiToolForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const toolData = {
        tool_name_cn: formData.get('toolNameCn'),
        tool_name_en: formData.get('toolNameEn'),
        description: formData.get('description'),
        service_path: formData.get('servicePath'),
        post_path: formData.get('postPath'),
        keywords: formData.get('keywords'),
        is_active: document.getElementById('isActive').checked,
        request_params: collectParams('requestParams'),
        response_params: collectParams('responseParams')
    };
    
    const url = currentEditingId ? `/api/tools/${currentEditingId}` : '/api/tools';
    const method = currentEditingId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(toolData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(currentEditingId ? '工具更新成功' : '工具添加成功', 'success');
            hideForm();
            loadTools();
        } else {
            showAlert('保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('保存失败: ' + error.message, 'error');
    });
});

// 删除工具
function deleteTool(toolId, toolName) {
    if (!confirm(`确定要删除工具 "${toolName}" 吗？此操作不可恢复。`)) {
        return;
    }
    
    fetch(`/api/tools/${toolId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('工具删除成功', 'success');
            loadTools();
        } else {
            showAlert('删除失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('删除失败: ' + error.message, 'error');
    });
}

// 添加请求参数
function addRequestParam() {
    addParam('requestParams');
}

// 添加返回参数
function addResponseParam() {
    addParam('responseParams');
}

// 添加参数
function addParam(containerId) {
    const container = document.getElementById(containerId);
    const paramDiv = document.createElement('div');
    paramDiv.className = 'param-item';
    
    paramDiv.innerHTML = `
        <input type="text" placeholder="参数名" class="param-name">
        <select class="param-type">
            <option value="string">字符串</option>
            <option value="int">整数</option>
            <option value="float">浮点数</option>
            <option value="boolean">布尔值</option>
            <option value="object">对象</option>
            <option value="array">数组</option>
        </select>
        <input type="text" placeholder="默认值" class="param-default">
        <input type="text" placeholder="描述" class="param-description">
        <button type="button" class="btn btn-danger" onclick="removeParam(this)">删除</button>
    `;
    
    container.appendChild(paramDiv);
}

// 删除参数
function removeParam(button) {
    button.parentElement.remove();
}

// 收集参数配置
function collectParams(containerId) {
    const container = document.getElementById(containerId);
    const params = {};
    
    container.querySelectorAll('.param-item').forEach(item => {
        const name = item.querySelector('.param-name').value;
        const type = item.querySelector('.param-type').value;
        const defaultValue = item.querySelector('.param-default').value;
        const description = item.querySelector('.param-description').value;
        
        if (name) {
            params[name] = {
                type: type,
                default: defaultValue,
                description: description
            };
        }
    });
    
    return params;
}

// 加载参数配置
function loadParams(containerId, params) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    
    Object.keys(params).forEach(name => {
        const param = params[name];
        const paramDiv = document.createElement('div');
        paramDiv.className = 'param-item';
        
        paramDiv.innerHTML = `
            <input type="text" placeholder="参数名" class="param-name" value="${name}">
            <select class="param-type">
                <option value="string" ${param.type === 'string' ? 'selected' : ''}>字符串</option>
                <option value="int" ${param.type === 'int' ? 'selected' : ''}>整数</option>
                <option value="float" ${param.type === 'float' ? 'selected' : ''}>浮点数</option>
                <option value="boolean" ${param.type === 'boolean' ? 'selected' : ''}>布尔值</option>
                <option value="object" ${param.type === 'object' ? 'selected' : ''}>对象</option>
                <option value="array" ${param.type === 'array' ? 'selected' : ''}>数组</option>
            </select>
            <input type="text" placeholder="默认值" class="param-default" value="${param.default || ''}">
            <input type="text" placeholder="描述" class="param-description" value="${param.description || ''}">
            <button type="button" class="btn btn-danger" onclick="removeParam(this)">删除</button>
        `;
        
        container.appendChild(paramDiv);
    });
}

// 清空参数配置
function clearParams() {
    document.getElementById('requestParams').innerHTML = '';
    document.getElementById('responseParams').innerHTML = '';
}

// 显示导入模态框
function showImportModal() {
    document.getElementById('importModal').style.display = 'block';
}

// 隐藏导入模态框
function hideImportModal() {
    document.getElementById('importModal').style.display = 'none';
    document.getElementById('importFile').value = '';
}

// 导入工具配置
function importTools() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];
    
    if (!file) {
        showAlert('请选择要导入的文件', 'error');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const toolsData = JSON.parse(e.target.result);
            
            fetch('/api/tools/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ tools: toolsData })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`导入成功: ${data.success_count} 个工具，失败: ${data.error_count} 个`, 'success');
                    hideImportModal();
                    loadTools();
                } else {
                    showAlert('导入失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showAlert('导入失败: ' + error.message, 'error');
            });
        } catch (error) {
            showAlert('文件格式错误，请确保是有效的JSON文件', 'error');
        }
    };
    
    reader.readAsText(file);
}

// 导出工具配置
function exportTools() {
    fetch('/api/tools/export')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const blob = new Blob([JSON.stringify(data.tools, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `api_tools_config_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                showAlert('配置导出成功', 'success');
            } else {
                showAlert('导出失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('导出失败: ' + error.message, 'error');
        });
}

// 显示提示信息
function showAlert(message, type) {
    const alertContainer = document.getElementById('alertContainer');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    alertContainer.appendChild(alertDiv);
    alertDiv.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
        alertDiv.style.display = 'none';
        alertContainer.removeChild(alertDiv);
    }, 3000);
}
