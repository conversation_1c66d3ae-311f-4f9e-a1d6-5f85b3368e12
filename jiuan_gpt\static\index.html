<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应急管理大模型平台</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: #1e3a8a;
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .nav-menu {
            display: flex;
        }
        
        .nav-menu a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-menu a:hover {
            background-color: #2d4eaf;
        }
        
        .hero {
            text-align: center;
            padding: 50px 0;
            background-color: #f0f5ff;
            margin-bottom: 30px;
            border-radius: 8px;
        }
        
        .hero h1 {
            font-size: 2.5em;
            margin-bottom: 15px;
            color: #1e3a8a;
        }
        
        .hero p {
            font-size: 1.2em;
            color: #555;
            max-width: 800px;
            margin: 0 auto 30px;
        }
        
        .features {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .feature-card {
            flex: 1;
            min-width: 300px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            padding: 25px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .feature-card h2 {
            color: #1e3a8a;
            margin-top: 0;
            font-size: 1.5em;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .feature-card a {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 20px;
            background-color: #1e3a8a;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .feature-card a:hover {
            background-color: #2d4eaf;
        }
        
        footer {
            text-align: center;
            padding: 20px;
            background-color: #1e3a8a;
            color: #ccc;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-content">
            <div class="logo">应急管理大模型平台</div>
            <div class="nav-menu">
                <a href="/" class="active">首页</a>
                <a href="/chat">智能对话</a>
                <a href="/emergency_qa">应急知识问答</a>
                <a href="/image_analysis">图片分析</a>
                <a href="/video_analysis">视频分析</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <section class="hero">
            <h1>应急管理大模型服务平台</h1>
            <p>基于先进的人工智能技术，提供智能对话、图片分析、视频分析等多种服务，助力应急管理智能化升级</p>
        </section>
        
        <section class="features">
            <div class="feature-card">
                <h2>智能对话</h2>
                <p>基于DeepSeek-r1满血版大模型，为您提供流畅、准确、专业的应急管理问答服务。支持多轮对话，深入理解您的问题，给出专业建议。</p>
                <a href="/chat">立即体验</a>
            </div>
            
            <div class="feature-card">
                <h2>图片分析</h2>
                <p>上传图片并提问，系统会精确识别图片内容并回答您的问题。可用于灾情识别、隐患排查、物资清点等多种应急场景。</p>
                <a href="/image_analysis">立即体验</a>
            </div>
            
            <div class="feature-card">
                <h2>视频分析</h2>
                <p>上传视频进行智能分析，自动识别视频中的关键信息，提取重要内容，辅助应急决策和事件回溯分析。</p>
                <a href="/video_analysis">立即体验</a>
            </div>
        </section>
    </div>
    
    <footer>
        <div class="container">
            <p>© 2024 应急管理大模型平台 版权所有</p>
        </div>
    </footer>
</body>
</html> 