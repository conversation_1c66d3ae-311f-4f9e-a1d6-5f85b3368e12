"""
应急语音识别（ASR）相关的API路由
"""
from fastapi import APIRouter, Request, HTTPException, Depends, UploadFile, File, Form
from fastapi.responses import JSONResponse
from fastapi.security import HTTPAuthorizationCredentials
from .auth import security, verify_token
from ..yijing_asr import YijingASRService
import logging
from typing import Optional
import configparser

logger = logging.getLogger(__name__)

def create_asr_router(config: configparser.ConfigParser):
    """创建ASR路由"""
    router = APIRouter()
    
    # 初始化ASR服务
    asr_service = YijingASRService(config)
    
    # 创建依赖注入函数
    def get_current_user_with_asr(credentials: HTTPAuthorizationCredentials = Depends(security)):
        """获取当前用户（ASR路由专用）"""
        try:
            username = verify_token(credentials)
            # 这里可以添加额外的用户验证逻辑
            return {"username": username}
        except Exception as e:
            raise HTTPException(status_code=401, detail="认证失败")
    
    @router.post("/api/asr/file/transfer")
    async def asr_file_transfer(
        audio_file: UploadFile = File(...),
        current_user: dict = Depends(get_current_user_with_asr)
    ):
        """
        文件转写接口
        上传PCM音频文件进行转写
        """
        try:
            if not asr_service.is_enabled:
                return JSONResponse(
                    status_code=503,
                    content={
                        "code": "0x0190001",
                        "msg": "ASR服务未启用",
                        "data": ""
                    }
                )
            
            # 调用ASR服务进行文件转写
            result = await asr_service.file_transfer(audio_file)
            
            if result["code"] == "0":
                logger.info(f"用户 {current_user['username']} 文件转写成功: {audio_file.filename}")
                return JSONResponse(content=result)
            else:
                return JSONResponse(
                    status_code=400,
                    content=result
                )
                
        except Exception as e:
            logger.error(f"文件转写API失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "code": "0x0190009",
                    "msg": "文件转写失败，请稍后重试",
                    "data": ""
                }
            )
    
    @router.post("/api/asr/end/task")
    async def asr_end_task(
        request: Request,
        current_user: dict = Depends(get_current_user_with_asr)
    ):
        """
        结束转写任务接口
        """
        try:
            if not asr_service.is_enabled:
                return JSONResponse(
                    status_code=503,
                    content={
                        "code": "0x0190001",
                        "msg": "ASR服务未启用",
                        "data": ""
                    }
                )
            
            data = await request.json()
            task_id = data.get("taskId", "").strip()
            
            if not task_id:
                return JSONResponse(
                    status_code=400,
                    content={
                        "code": "0x019000A",
                        "msg": "任务ID不能为空",
                        "data": ""
                    }
                )
            
            # 调用ASR服务结束任务
            result = await asr_service.end_task(task_id)
            
            if result["code"] == "0":
                logger.info(f"用户 {current_user['username']} 结束任务成功: {task_id}")
                return JSONResponse(content=result)
            else:
                return JSONResponse(
                    status_code=400,
                    content=result
                )
                
        except Exception as e:
            logger.error(f"结束任务API失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "code": "0x019000B",
                    "msg": "结束任务失败，请稍后重试",
                    "data": ""
                }
            )
    
    @router.get("/api/asr/websocket/url")
    async def get_websocket_url(
        mode: str = "single",
        creator: Optional[str] = None,
        speaker: Optional[str] = None,
        task_id: Optional[str] = None,
        current_user: dict = Depends(get_current_user_with_asr)
    ):
        """
        获取WebSocket连接URL
        """
        try:
            if not asr_service.is_enabled:
                return JSONResponse(
                    status_code=503,
                    content={
                        "code": "0x0190001",
                        "msg": "ASR服务未启用",
                        "data": ""
                    }
                )
            
            # 设置默认值
            if not creator:
                creator = current_user['username']
            if not speaker:
                speaker = current_user['username']
            
            # 验证模式参数
            if mode not in ["single", "meeting"]:
                return JSONResponse(
                    status_code=400,
                    content={
                        "code": "0x019000C",
                        "msg": "不支持的转写模式，只支持single或meeting",
                        "data": ""
                    }
                )
            
            # 创建实时转写会话
            session_result = asr_service.create_realtime_session(
                mode=mode,
                creator=creator,
                speaker=speaker,
                task_id=task_id
            )
            
            if session_result["success"]:
                logger.info(f"用户 {current_user['username']} 获取WebSocket URL成功: {mode}模式")
                return JSONResponse(content={
                    "code": "0",
                    "msg": "获取WebSocket URL成功",
                    "data": {
                        "url": session_result["session_info"]["url"],
                        "mode": mode,
                        "creator": creator,
                        "speaker": speaker,
                        "task_id": task_id
                    }
                })
            else:
                return JSONResponse(
                    status_code=400,
                    content={
                        "code": "0x019000D",
                        "msg": session_result["message"],
                        "data": ""
                    }
                )
                
        except Exception as e:
            logger.error(f"获取WebSocket URL失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "code": "0x019000E",
                    "msg": "获取WebSocket URL失败，请稍后重试",
                    "data": ""
                }
            )
    
    @router.get("/api/asr/tasks")
    async def get_active_tasks(
        current_user: dict = Depends(get_current_user_with_asr)
    ):
        """
        获取活跃任务列表
        """
        try:
            if not asr_service.is_enabled:
                return JSONResponse(
                    status_code=503,
                    content={
                        "code": "0x0190001",
                        "msg": "ASR服务未启用",
                        "data": ""
                    }
                )
            
            active_tasks = asr_service.get_active_tasks()
            
            logger.info(f"用户 {current_user['username']} 获取活跃任务列表: {len(active_tasks)}个任务")
            return JSONResponse(content={
                "code": "0",
                "msg": "获取任务列表成功",
                "data": {
                    "total": len(active_tasks),
                    "list": active_tasks
                }
            })
                
        except Exception as e:
            logger.error(f"获取任务列表失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "code": "0x019000F",
                    "msg": "获取任务列表失败，请稍后重试",
                    "data": ""
                }
            )
    
    @router.get("/api/asr/task/{task_id}")
    async def get_task_info(
        task_id: str,
        current_user: dict = Depends(get_current_user_with_asr)
    ):
        """
        获取任务详细信息
        """
        try:
            if not asr_service.is_enabled:
                return JSONResponse(
                    status_code=503,
                    content={
                        "code": "0x0190001",
                        "msg": "ASR服务未启用",
                        "data": ""
                    }
                )
            
            task_info = asr_service.get_task_info(task_id)
            
            if task_info:
                logger.info(f"用户 {current_user['username']} 获取任务信息成功: {task_id}")
                return JSONResponse(content={
                    "code": "0",
                    "msg": "获取任务信息成功",
                    "data": task_info
                })
            else:
                return JSONResponse(
                    status_code=404,
                    content={
                        "code": "0x0190010",
                        "msg": "任务不存在",
                        "data": ""
                    }
                )
                
        except Exception as e:
            logger.error(f"获取任务信息失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "code": "0x0190011",
                    "msg": "获取任务信息失败，请稍后重试",
                    "data": ""
                }
            )
    
    @router.get("/api/asr/status")
    async def get_asr_status(
        current_user: dict = Depends(get_current_user_with_asr)
    ):
        """
        获取ASR服务状态
        """
        try:
            is_available = asr_service.is_service_available()
            
            return JSONResponse(content={
                "code": "0",
                "msg": "获取服务状态成功",
                "data": {
                    "enabled": asr_service.is_enabled,
                    "available": is_available,
                    "base_url": asr_service.base_url if asr_service.is_enabled else "",
                    "supported_formats": asr_service.supported_formats if asr_service.is_enabled else [],
                    "max_audio_size_mb": asr_service.max_audio_size_mb if asr_service.is_enabled else 0
                }
            })
                
        except Exception as e:
            logger.error(f"获取ASR服务状态失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "code": "0x0190012",
                    "msg": "获取服务状态失败，请稍后重试",
                    "data": ""
                }
            )
    
    return router 