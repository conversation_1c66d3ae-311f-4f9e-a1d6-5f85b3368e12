<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API工具配置管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
        }

        .toolbar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .btn-warning {
            background-color: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background-color: #e67e22;
        }

        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .form-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: none;
        }

        .form-container.active {
            display: block;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .params-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background-color: #f9f9f9;
        }

        .param-item {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }

        .param-item input {
            flex: 1;
        }

        .param-item select {
            width: 120px;
        }

        .param-item .btn {
            padding: 5px 10px;
            font-size: 12px;
        }

        .tools-list {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .tool-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .tool-item:last-child {
            border-bottom: none;
        }

        .tool-info h3 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .tool-info p {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 3px;
        }

        .tool-actions {
            display: flex;
            gap: 5px;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .hidden {
            display: none;
        }

        .file-input {
            margin-bottom: 10px;
        }

        .alert {
            padding: 12px;
            margin-bottom: 15px;
            border-radius: 4px;
            display: none;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>API工具配置管理</h1>
            <p>管理系统中的API工具配置，支持添加、编辑、删除和导入导出功能</p>
        </div>

        <div class="toolbar">
            <button class="btn btn-primary" onclick="showAddForm()">
                <i class="icon">+</i> 添加工具
            </button>
            <button class="btn btn-success" onclick="showImportModal()">
                <i class="icon">↑</i> 导入配置
            </button>
            <button class="btn btn-warning" onclick="exportTools()">
                <i class="icon">↓</i> 导出配置
            </button>
            <button class="btn btn-primary" onclick="loadTools()">
                <i class="icon">↻</i> 刷新列表
            </button>
        </div>

        <div id="alertContainer"></div>

        <!-- 添加/编辑表单 -->
        <div id="toolForm" class="form-container">
            <h2 id="formTitle">添加API工具</h2>
            <form id="apiToolForm">
                <input type="hidden" id="toolId" name="toolId">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="toolNameCn">工具名称（中文）*</label>
                        <input type="text" id="toolNameCn" name="toolNameCn" required>
                    </div>
                    <div class="form-group">
                        <label for="toolNameEn">工具名称（英文）*</label>
                        <input type="text" id="toolNameEn" name="toolNameEn" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">工具描述</label>
                    <textarea id="description" name="description" placeholder="描述工具的功能和用途"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="servicePath">服务段*</label>
                        <input type="text" id="servicePath" name="servicePath" placeholder="/emdispatch-web" required>
                    </div>
                    <div class="form-group">
                        <label for="postPath">POST路径*</label>
                        <input type="text" id="postPath" name="postPath" placeholder="/emdispatch-web/third/v1/eventApi/app/" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="keywords">关键词组合*</label>
                    <textarea id="keywords" name="keywords" placeholder="用逗号分隔的关键词，如：查询,事件,周边,资源" required></textarea>
                </div>

                <div class="form-group">
                    <label>请求参数配置</label>
                    <div class="params-container">
                        <div id="requestParams">
                            <!-- 动态生成的参数配置 -->
                        </div>
                        <button type="button" class="btn btn-primary" onclick="addRequestParam()">添加参数</button>
                    </div>
                </div>

                <div class="form-group">
                    <label>返回参数配置</label>
                    <div class="params-container">
                        <div id="responseParams">
                            <!-- 动态生成的参数配置 -->
                        </div>
                        <button type="button" class="btn btn-primary" onclick="addResponseParam()">添加参数</button>
                    </div>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="isActive" name="isActive" checked>
                        启用此工具
                    </label>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-success">保存工具</button>
                    <button type="button" class="btn btn-danger" onclick="hideForm()">取消</button>
                </div>
            </form>
        </div>

        <!-- 工具列表 -->
        <div class="tools-list">
            <div id="toolsList" class="loading">
                正在加载工具列表...
            </div>
        </div>
    </div>

    <!-- 导入模态框 -->
    <div id="importModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>导入API工具配置</h2>
                <span class="close" onclick="hideImportModal()">&times;</span>
            </div>
            <div class="file-input">
                <label for="importFile">选择配置文件（JSON格式）：</label>
                <input type="file" id="importFile" accept=".json">
            </div>
            <div>
                <button class="btn btn-success" onclick="importTools()">导入</button>
                <button class="btn btn-danger" onclick="hideImportModal()">取消</button>
            </div>
        </div>
    </div>

    <script src="api_tools_config.js"></script>
</body>
</html>
