<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片分析 - 应急管理大模型平台</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        header {
            background-color: #1e3a8a;
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .nav-menu {
            display: flex;
        }
        
        .nav-menu a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-menu a:hover {
            background-color: #2d4eaf;
        }
        
        .nav-menu a.active {
            background-color: #2d4eaf;
        }
        
        main {
            flex: 1;
            padding: 20px 0;
        }
        
        .page-title {
            margin-bottom: 20px;
            color: #1e3a8a;
        }
        
        .analysis-container {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .upload-section {
            flex: 1;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            padding: 20px;
        }
        
        .result-section {
            flex: 1;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        
        .drag-area {
            border: 2px dashed #1e3a8a;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: background-color 0.3s;
            cursor: pointer;
        }
        
        .drag-area.active {
            background-color: #f0f5ff;
        }
        
        .drag-area .icon {
            font-size: 50px;
            color: #1e3a8a;
            margin-bottom: 15px;
        }
        
        .drag-area h3 {
            margin: 0 0 10px;
            color: #1e3a8a;
        }
        
        .drag-area p {
            margin: 0;
            color: #666;
        }
        
        .file-info {
            display: none;
            margin-top: 20px;
            text-align: left;
            padding: 10px;
            background-color: #f0f5ff;
            border-radius: 4px;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 250px;
            margin-top: 10px;
            border-radius: 4px;
            display: block;
        }
        
        .query-input {
            display: flex;
            margin-top: 20px;
        }
        
        .query-input input {
            flex: 1;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            font-size: 14px;
        }
        
        .query-input input:focus {
            outline: none;
            border-color: #1e3a8a;
        }
        
        button {
            padding: 10px 20px;
            background-color: #1e3a8a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: bold;
        }
        
        button:hover {
            background-color: #2d4eaf;
        }
        
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .query-input button {
            margin-left: 10px;
        }
        
        .analysis-title {
            margin-top: 0;
            color: #1e3a8a;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .analysis-content {
            flex: 1;
            overflow-y: auto;
            padding: 10px 0;
            line-height: 1.6;
        }
        
        .result-image-container {
            margin-top: 20px;
            text-align: center;
        }
        
        .loading {
            text-align: center;
            padding: 30px;
            color: #666;
        }
        
        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid rgba(30, 58, 138, 0.2);
            border-radius: 50%;
            border-top-color: #1e3a8a;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            padding: 10px;
            background-color: #fee2e2;
            color: #b91c1c;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        footer {
            text-align: center;
            padding: 20px;
            background-color: #1e3a8a;
            color: #ccc;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .analysis-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-content">
            <div class="logo">应急管理大模型平台</div>
            <div class="nav-menu">
                <a href="/">首页</a>
                <a href="/chat">智能对话</a>
                <a href="/emergency_qa">应急知识问答</a>
                <a href="/image_analysis" class="active">图片分析</a>
                <a href="/video_analysis">视频分析</a>
            </div>
        </div>
    </header>
    
    <main class="container">
        <h1 class="page-title">图片分析</h1>
        
        <div class="analysis-container">
            <section class="upload-section">
                <div id="drag-area" class="drag-area">
                    <div class="icon">📷</div>
                    <h3>拖放图片文件到这里</h3>
                    <p>或者 <strong>点击浏览</strong></p>
                    <input type="file" id="file-input" hidden accept="image/*">
                </div>
                
                <div id="file-info" class="file-info">
                    <div>
                        <strong>已选择文件:</strong> <span id="file-name"></span>
                    </div>
                    <div>
                        <strong>文件大小:</strong> <span id="file-size"></span>
                    </div>
                    <img id="preview-image" class="preview-image" src="" alt="预览图">
                </div>
                
                <div class="query-input">
                    <input type="text" id="query-input" placeholder="请输入您要提问的内容，例如：这张图片上有什么？">
                    <button id="analyze-btn" disabled>分析图片</button>
                </div>
            </section>
            
            <section class="result-section">
                <h2 class="analysis-title">分析结果</h2>
                <div id="analysis-content" class="analysis-content">
                    <p>请上传图片并提问，系统将对图片内容进行分析。</p>
                </div>
            </section>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>© 2024 应急管理大模型平台 版权所有</p>
        </div>
    </footer>
    
    <script>
        // 获取DOM元素
        const dragArea = document.getElementById('drag-area');
        const fileInput = document.getElementById('file-input');
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        const previewImage = document.getElementById('preview-image');
        const queryInput = document.getElementById('query-input');
        const analyzeBtn = document.getElementById('analyze-btn');
        const analysisContent = document.getElementById('analysis-content');
        
        // 当前选择的文件
        let selectedFile = null;
        
        // 点击拖放区域触发文件选择
        dragArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // 监听文件选择变化
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                handleFile(this.files[0]);
            }
        });
        
        // 拖放事件监听
        ['dragover', 'dragleave', 'drop'].forEach(eventName => {
            dragArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        // 拖放视觉反馈
        dragArea.addEventListener('dragover', function() {
            this.classList.add('active');
        });
        
        dragArea.addEventListener('dragleave', function() {
            this.classList.remove('active');
        });
        
        // 处理拖放的文件
        dragArea.addEventListener('drop', function(e) {
            this.classList.remove('active');
            
            if (e.dataTransfer.files.length > 0) {
                handleFile(e.dataTransfer.files[0]);
            }
        });
        
        // 处理选择的文件
        function handleFile(file) {
            // 检查是否是图片文件
            if (!file.type.match('image.*')) {
                alert('请选择图片文件！');
                return;
            }
            
            selectedFile = file;
            
            // 显示文件信息
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';
            
            // 显示图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
            };
            reader.readAsDataURL(file);
            
            // 启用分析按钮
            analyzeBtn.disabled = false;
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 分析按钮点击事件
        analyzeBtn.addEventListener('click', analyzeImage);
        
        // 分析图片
        function analyzeImage() {
            if (!selectedFile) {
                alert('请先选择一张图片！');
                return;
            }
            
            // 准备要发送的数据
            const formData = new FormData();
            formData.append('image', selectedFile);
            formData.append('query', queryInput.value || '请描述这张图片');
            
            // 禁用分析按钮
            analyzeBtn.disabled = true;
            
            // 显示加载中
            analysisContent.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在分析图片，请稍候...</p>
                </div>
            `;
            
            // 发送请求
            fetch('/analyze_image/', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // 处理响应
                if (data.status === 'success' && data.data) {
                    // 提取分析结果
                    let analysisResult = '';
                    
                    // 根据API响应结构提取结果
                    if (data.data.result) {
                        analysisResult = data.data.result;
                    } else if (data.data.msg) {
                        analysisResult = data.data.msg;
                    } else {
                        analysisResult = '分析完成，但未能提取结果内容。';
                    }
                    
                    // 处理可能的检测框信息
                    let boxesHtml = '';
                    if (data.data.boxList && data.data.boxList.length > 0) {
                        boxesHtml = '<div class="result-image-container">';
                        boxesHtml += '<h3>检测结果</h3>';
                        // 这里应添加渲染检测框的逻辑，可能需要canvas绘制
                        boxesHtml += '<p>系统检测到 ' + data.data.boxList.length + ' 个目标</p>';
                        boxesHtml += '</div>';
                    }
                    
                    // 更新分析结果内容
                    analysisContent.innerHTML = `
                        <div>${analysisResult.replace(/\n/g, '<br>')}</div>
                        ${boxesHtml}
                    `;
                } else {
                    // 显示错误信息
                    const errorMessage = data.message || '分析过程中发生未知错误';
                    analysisContent.innerHTML = `
                        <div class="error-message">
                            <strong>错误:</strong> ${errorMessage}
                        </div>
                    `;
                }
            })
            .catch(error => {
                // 显示错误信息
                analysisContent.innerHTML = `
                    <div class="error-message">
                        <strong>网络错误:</strong> ${error.message}
                    </div>
                `;
            })
            .finally(() => {
                // 启用分析按钮
                analyzeBtn.disabled = false;
            });
        }
    </script>
</body>
</html> 