import httpx
import json
import uuid

async def emergency_qa_query(query, config, logger, conversation_id=None):
    """
    应急行业知识问答
    
    Args:
        query: 用户提问内容
        config: 配置对象
        logger: 日志对象
        conversation_id: 会话ID，如果不提供则生成新的
        
    Returns:
        dict: 知识问答结果
    """
    try:
        # 获取配置信息
        url = config.get("API", "EMERGENCY_QA_URL")
        content_type = config.get("API", "EMERGENCY_QA_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("API", "EMERGENCY_QA_TIMEOUT", fallback=120)
        token = config.get("API", "TOKEN", fallback="")
        verify_ssl = config.getboolean("API", "VERIFY_SSL", fallback=False)
        
        logger.info(f"准备调用应急知识问答接口: {url}")
        logger.info(f"用户提问: {query}")
        
        # 如果没有提供conversation_id，生成一个新的
        if not conversation_id:
            conversation_id = str(uuid.uuid4())
            
        # 准备请求头
        headers = {
            "Content-Type": content_type,
            "Accept": "application/json"
        }
        
        # 添加认证头
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        # 准备请求体 - 根据图片中的应急知识问答接口格式
        payload = {
            "query": query
        }
        
        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到应急知识问答接口")
            response = await client.post(
                url, 
                json=payload, 
                headers=headers, 
                timeout=timeout
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"应急知识问答接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }
                
            # 解析响应内容
            response_data = response.json()
            logger.info(f"应急知识问答接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")
            
            # 返回结果
            return {
                "status": "success",
                "data": response_data,
                "conversation_id": conversation_id
            }
                
    except Exception as e:
        logger.error(f"调用应急知识问答接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用应急知识问答接口时出错: {str(e)}"
        }

async def get_emergency_knowledge_categories(config, logger):
    """
    获取应急知识分类（如果接口支持）
    
    Args:
        config: 配置对象
        logger: 日志对象
        
    Returns:
        dict: 知识分类结果
    """
    try:
        # 获取配置信息
        categories_url = config.get("API", "EMERGENCY_CATEGORIES_URL", fallback=None)
        
        if not categories_url:
            # 如果没有配置分类接口，返回默认分类
            default_categories = [
                {"id": "disaster", "name": "自然灾害", "description": "地震、洪水、台风等自然灾害应急知识"},
                {"id": "fire", "name": "火灾应急", "description": "火灾预防、逃生、救援等知识"},
                {"id": "safety", "name": "安全生产", "description": "生产安全、职业健康等知识"},
                {"id": "medical", "name": "医疗急救", "description": "急救技能、医疗救护等知识"},
                {"id": "security", "name": "公共安全", "description": "社会安全、治安防范等知识"},
                {"id": "environment", "name": "环境应急", "description": "环境污染、化学品泄漏等应急知识"}
            ]
            return {
                "status": "success",
                "data": {"categories": default_categories}
            }
        
        # 如果配置了分类接口，调用获取
        content_type = config.get("API", "EMERGENCY_QA_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("API", "EMERGENCY_QA_TIMEOUT", fallback=60)
        token = config.get("API", "TOKEN", fallback="")
        verify_ssl = config.getboolean("API", "VERIFY_SSL", fallback=False)
        
        headers = {
            "Content-Type": content_type,
            "Accept": "application/json"
        }
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            response = await client.get(
                categories_url, 
                headers=headers, 
                timeout=timeout
            )
            
            if response.status_code == 200:
                response_data = response.json()
                return {
                    "status": "success",
                    "data": response_data
                }
            else:
                logger.warning(f"获取知识分类失败: {response.status_code}")
                # 返回默认分类
                return await get_emergency_knowledge_categories(config, logger)
                
    except Exception as e:
        logger.error(f"获取应急知识分类时出错: {str(e)}")
        # 返回默认分类
        default_categories = [
            {"id": "general", "name": "综合应急", "description": "综合应急管理知识"}
        ]
        return {
            "status": "success",
            "data": {"categories": default_categories}
        } 