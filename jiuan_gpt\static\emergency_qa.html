<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应急行业知识问答 - 应急管理大模型平台</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        header {
            background-color: #1e3a8a;
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .nav-menu {
            display: flex;
        }
        
        .nav-menu a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-menu a:hover {
            background-color: #2d4eaf;
        }
        
        .nav-menu a.active {
            background-color: #2d4eaf;
        }
        
        main {
            flex: 1;
            padding: 20px 0;
        }
        
        .page-title {
            margin-bottom: 20px;
            color: #1e3a8a;
            text-align: center;
        }
        
        .qa-container {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            height: calc(100vh - 200px);
        }
        
        .chat-section {
            flex: 1;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            display: flex;
            flex-direction: column;
        }
        
        .qa-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.bot {
            justify-content: flex-start;
        }
        
        .message-content {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .user .message-content {
            background-color: #1e3a8a;
            color: white;
            border-top-right-radius: 0;
        }
        
        .bot .message-content {
            background-color: #f0f5ff;
            color: #333;
            border-top-left-radius: 0;
        }
        
        .qa-input {
            display: flex;
            padding: 15px 20px;
            background-color: #f9f9f9;
        }
        
        .qa-input textarea {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            height: 60px;
            transition: border-color 0.3s;
        }
        
        .qa-input textarea:focus {
            outline: none;
            border-color: #1e3a8a;
        }
        
        .qa-input button {
            margin-left: 10px;
            padding: 0 20px;
            background-color: #1e3a8a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: bold;
        }
        
        .qa-input button:hover {
            background-color: #2d4eaf;
        }
        
        .qa-input button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .typing-indicator {
            display: inline-block;
            padding: 10px 15px;
            background-color: #f0f5ff;
            border-radius: 8px;
            margin-bottom: 20px;
            color: #666;
        }
        
        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #1e3a8a;
            border-radius: 50%;
            margin-right: 5px;
            animation: typing 1s infinite;
        }
        
        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-5px); }
        }
        
        .suggested-questions {
            margin-top: 15px;
            padding: 15px 20px;
            background-color: #f9f9f9;
            border-top: 1px solid #eee;
        }
        
        .suggested-questions h4 {
            margin: 0 0 10px;
            color: #1e3a8a;
            font-size: 1em;
        }
        
        .question-tag {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px;
            background-color: #e5e7eb;
            border-radius: 12px;
            font-size: 0.8em;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .question-tag:hover {
            background-color: #1e3a8a;
            color: white;
        }
        
        footer {
            text-align: center;
            padding: 20px;
            background-color: #1e3a8a;
            color: #ccc;
        }
        
        @media (max-width: 768px) {
            .qa-container {
                height: calc(100vh - 150px);
            }
            
            .chat-section {
                height: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-content">
            <div class="logo">应急管理大模型平台</div>
            <div class="nav-menu">
                <a href="/">首页</a>
                <a href="/chat">智能对话</a>
                <a href="/emergency_qa" class="active">应急知识问答</a>
                <a href="/image_analysis">图片分析</a>
                <a href="/video_analysis">视频分析</a>
            </div>
        </div>
    </header>
    
    <main class="container">
        <h1 class="page-title">应急行业知识问答</h1>
        
        <div class="qa-container">
            <section class="chat-section">
                <div id="qa-messages" class="qa-messages">
                    <div class="message bot">
                        <div class="message-content">
                            您好，我是应急管理知识问答助手！您可以向我询问各种应急管理相关问题，包括自然灾害防护、安全生产、急救知识等。请直接提问。
                        </div>
                    </div>
                </div>
                
                <div class="qa-input">
                    <textarea id="question-input" placeholder="请输入您的问题，例如：地震发生时应该如何自救？"></textarea>
                    <button id="ask-button">提问</button>
                </div>
                
                <div class="suggested-questions">
                    <h4>常见问题</h4>
                    <div class="question-tag" onclick="askPredefinedQuestion('地震发生时如何自救？')">地震自救</div>
                    <div class="question-tag" onclick="askPredefinedQuestion('火灾逃生的基本原则是什么？')">火灾逃生</div>
                    <div class="question-tag" onclick="askPredefinedQuestion('台风来临前需要做哪些准备？')">台风防护</div>
                    <div class="question-tag" onclick="askPredefinedQuestion('如何进行心肺复苏？')">急救知识</div>
                    <div class="question-tag" onclick="askPredefinedQuestion('危险化学品泄漏如何处理？')">化学品安全</div>
                    <div class="question-tag" onclick="askPredefinedQuestion('如何制定应急预案？')">应急预案</div>
                </div>
            </section>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>© 2024 应急管理大模型平台 版权所有</p>
        </div>
    </footer>
    
    <script>
        // 生成唯一会话ID
        const sessionId = 'emergency_qa_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
        let conversationId = null;
        
        // 获取DOM元素
        const qaMessages = document.getElementById('qa-messages');
        const questionInput = document.getElementById('question-input');
        const askButton = document.getElementById('ask-button');
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定事件
            askButton.addEventListener('click', askQuestion);
            questionInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    askQuestion();
                }
            });
        });
        
        // 提问预定义问题
        function askPredefinedQuestion(question) {
            questionInput.value = question;
            askQuestion();
        }
        
        // 提问函数
        function askQuestion() {
            const question = questionInput.value.trim();
            if (!question) {
                alert('请输入您的问题！');
                return;
            }
            
            // 添加用户消息到聊天界面
            addMessage(question, 'user');
            
            // 清空输入框
            questionInput.value = '';
            
            // 显示正在思考
            const typingIndicator = document.createElement('div');
            typingIndicator.className = 'typing-indicator';
            typingIndicator.innerHTML = '<span></span><span></span><span></span> 正在查找相关知识...';
            qaMessages.appendChild(typingIndicator);
            
            // 自动滚动到底部
            qaMessages.scrollTop = qaMessages.scrollHeight;
            
            // 禁用提问按钮
            askButton.disabled = true;
            
            // 准备请求体
            const requestBody = { 
                query: question
            };
            
            // 如果有conversation_id，添加到请求中
            if (conversationId) {
                requestBody.conversation_id = conversationId;
            }
            
            // 发送API请求
            fetch('/emergency_qa/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'session-id': sessionId
                },
                body: JSON.stringify(requestBody)
            })
            .then(response => response.json())
            .then(data => {
                // 移除输入指示器
                qaMessages.removeChild(typingIndicator);
                
                // 处理响应
                if (data.status === 'success' && data.data) {
                    // 保存conversation_id以便后续使用
                    if (data.conversation_id) {
                        conversationId = data.conversation_id;
                    }
                    
                    // 提取回复文本
                    let answer = "抱歉，我没有找到相关的知识内容。";
                    
                    // 根据API返回格式提取回复
                    if (data.data.answer) {
                        answer = data.data.answer;
                    } else if (data.data.response) {
                        answer = data.data.response;
                    } else if (data.data.result) {
                        answer = data.data.result;
                    } else if (data.data.msg) {
                        answer = data.data.msg;
                    } else if (data.data.content) {
                        answer = data.data.content;
                    }
                    
                    // 添加机器人回复
                    addMessage(answer, 'bot');
                } else {
                    // 处理错误
                    addMessage('抱歉，查询知识时发生错误: ' + (data.message || '未知错误'), 'bot');
                }
                
                // 启用提问按钮
                askButton.disabled = false;
            })
            .catch(error => {
                // 移除输入指示器
                qaMessages.removeChild(typingIndicator);
                
                // 添加错误消息
                addMessage('网络错误，请稍后重试: ' + error.message, 'bot');
                
                // 启用提问按钮
                askButton.disabled = false;
            });
        }
        
        // 添加消息到聊天界面
        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + sender;
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            
            // 处理换行符和格式化
            messageContent.innerHTML = text.replace(/\n/g, '<br>');
            
            messageDiv.appendChild(messageContent);
            qaMessages.appendChild(messageDiv);
            
            // 自动滚动到底部
            qaMessages.scrollTop = qaMessages.scrollHeight;
        }
    </script>
</body>
</html> 