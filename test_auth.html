<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            background-color: #f8f9fa;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>认证系统测试页面</h1>
    
    <div class="test-section">
        <h3>当前登录状态</h3>
        <p>Token: <span id="current-token">未检测到</span></p>
        <p>用户信息: <span id="current-user">未检测到</span></p>
        <button onclick="checkCurrentStatus()">刷新状态</button>
    </div>
    
    <div class="test-section">
        <h3>Token验证测试</h3>
        <button onclick="testTokenVerification()">测试Token验证</button>
        <div id="token-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>API调用测试</h3>
        <button onclick="testGetSessions()">测试获取会话列表</button>
        <button onclick="testAdminAPI()">测试管理员API</button>
        <div id="api-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>操作</h3>
        <button onclick="clearStorage()">清除本地存储</button>
        <button onclick="goToLogin()">跳转到登录页面</button>
        <button onclick="goToApp()">跳转到主应用</button>
    </div>

    <script>
        // 检查当前状态
        function checkCurrentStatus() {
            const token = localStorage.getItem('user_token');
            const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
            
            document.getElementById('current-token').textContent = token ? token.substring(0, 20) + '...' : '未检测到';
            document.getElementById('current-user').textContent = userInfo.username ? `${userInfo.username} (${userInfo.role})` : '未检测到';
        }
        
        // 测试Token验证
        async function testTokenVerification() {
            const resultDiv = document.getElementById('token-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试中...';
            
            const token = localStorage.getItem('user_token');
            if (!token) {
                resultDiv.textContent = '错误: 没有找到Token';
                resultDiv.className = 'result error';
                return;
            }
            
            try {
                const response = await fetch('/api/verify-token', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.textContent = `成功: Token有效，用户: ${data.user.username}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `失败: HTTP ${response.status}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试获取会话列表
        async function testGetSessions() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试获取会话列表...';
            
            const token = localStorage.getItem('user_token');
            if (!token) {
                resultDiv.textContent = '错误: 没有找到Token';
                resultDiv.className = 'result error';
                return;
            }
            
            try {
                const response = await fetch('/get_sessions/', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.textContent = `成功: 获取到 ${data.length} 个会话`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `失败: HTTP ${response.status}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试管理员API
        async function testAdminAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试管理员API...';
            
            const token = localStorage.getItem('user_token');
            if (!token) {
                resultDiv.textContent = '错误: 没有找到Token';
                resultDiv.className = 'result error';
                return;
            }
            
            try {
                const response = await fetch('/api/admin/pending-users', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.textContent = `成功: 管理员API正常，待审核用户: ${data.users ? data.users.length : 0}`;
                    resultDiv.className = 'result success';
                } else if (response.status === 403) {
                    resultDiv.textContent = '失败: 权限不足（非管理员）';
                    resultDiv.className = 'result error';
                } else {
                    resultDiv.textContent = `失败: HTTP ${response.status}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 清除本地存储
        function clearStorage() {
            localStorage.removeItem('user_token');
            localStorage.removeItem('user_info');
            checkCurrentStatus();
            alert('本地存储已清除');
        }
        
        // 跳转到登录页面
        function goToLogin() {
            window.location.href = '/login';
        }
        
        // 跳转到主应用
        function goToApp() {
            window.location.href = '/app';
        }
        
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkCurrentStatus();
        });
    </script>
</body>
</html>
