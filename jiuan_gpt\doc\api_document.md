# 久安应急管理大模型平台 API 文档

## 1 系统概述

久安应急管理大模型平台基于DeepSeek-r1满血版大模型，提供多模态AI服务，包括智能对话、图片分析、视频分析和应急知识问答等功能。系统采用FastAPI框架构建，提供RESTful API接口服务。

### 1.1 系统架构

- **前端界面**：基于HTML5和JavaScript的响应式Web界面
- **后端服务**：FastAPI + Python异步框架
- **AI模型**：集成DeepSeek-r1、图片识别、视频分析等多个AI模型
- **文件处理**：支持图片、视频等多媒体文件的上传和处理

### 1.2 支持的功能模块

1. **智能对话** - 基于DeepSeek-r1的自然语言对话
2. **图片分析** - 图像内容识别和分析
3. **视频分析** - 视频内容理解和事件检测  
4. **应急知识问答** - 专业应急管理知识咨询

## 2 通用说明

### 2.1 接口基础信息

- **基础URL**: `http://{host}:{port}`
- **默认端口**: 8088
- **内容类型**: application/json
- **字符编码**: UTF-8

### 2.2 请求头参数

| 参数名        | 参数类型   | 是否必须 | 说明                                    |
| ---------- | ------ | ---- | ------------------------------------- |
| session-id | String | 否    | 会话标识，用于维护用户会话上下文，不提供时系统自动生成        |
| Content-Type | String | 是    | 请求内容类型，通常为 application/json 或 multipart/form-data |

### 2.3 通用响应格式

```json
{
  "status": "success|error|timeout",
  "message": "状态描述信息",
  "data": {},
  "conversation_id": "会话ID（可选）",
  "task_id": "任务ID（异步操作）",
  "elapsed_time": "处理时间（秒）"
}
```

### 2.4 错误码说明

| 错误码 | 说明            | 处理建议              |
| --- | ------------- | ----------------- |
| 200 | 请求成功          | -                 |
| 400 | 请求参数错误        | 检查请求参数格式和必填项      |
| 404 | 接口不存在         | 检查请求URL是否正确       |
| 413 | 文件大小超限        | 减小文件大小后重新上传       |
| 422 | 文件格式不支持       | 使用支持的文件格式         |
| 500 | 服务器内部错误       | 稍后重试或联系技术支持       |

## 3 智能对话模块

### 3.1 智能对话接口

#### 接口描述

基于DeepSeek-r1大模型的智能对话接口，支持多轮对话和上下文理解。

#### 接口版本

v1.0

#### 接口地址

POST /chat/

#### 请求参数

| 参数名             | 参数类型   | 是否必须 | 说明                            |
| --------------- | ------ | ---- | ----------------------------- |
| message         | String | 是    | 用户发送的消息内容                     |
| conversation_id | String | 否    | 对话会话ID，用于维护多轮对话上下文，不提供时系统自动生成 |

#### 入参示例

```json
{
  "message": "地震发生时应该如何自救？",
  "conversation_id": "chat_1234567890_123"
}
```

#### 返回参数

| 参数名             | 参数类型   | 说明                 |
| --------------- | ------ | ------------------ |
| status          | String | 处理状态：success/error |
| data            | Object | 模型回复内容             |
| conversation_id | String | 会话ID，用于后续对话        |

#### 返回示例

```json
{
  "status": "success",
  "data": {
    "response": "地震发生时的自救措施包括：\n1. 保持冷静，不要惊慌\n2. 立即就近躲避，钻到桌子下面或躲在墙角\n3. 用手或软物保护头部\n4. 远离玻璃窗、吊灯等危险物品\n5. 震后迅速撤离到开阔地带\n6. 如被困废墟，要保存体力，敲击物体发出声音求救"
  },
  "conversation_id": "chat_1234567890_123"
}
```

### 3.2 对话页面

#### 接口描述

获取智能对话的Web界面页面。

#### 接口地址

GET /chat

#### 返回内容

返回HTML页面文件，提供用户友好的对话界面。

## 4 图片分析模块

### 4.1 图片分析接口

#### 接口描述

上传图片进行AI分析，可以识别图片内容并回答用户关于图片的问题。

#### 接口版本

v1.0

#### 接口地址

POST /analyze_image/

#### 请求参数

| 参数名   | 参数类型   | 是否必须 | 说明                                              |
| ----- | ------ | ---- | ----------------------------------------------- |
| image | File   | 是    | 上传的图片文件，支持常见图片格式（JPG、PNG、GIF、BMP等）          |
| query | String | 否    | 用户对图片的提问内容，不提供时默认为"请描述这张图片"               |

#### 文件限制

- **支持格式**: JPG, JPEG, PNG, GIF, BMP, WEBP
- **文件大小**: 无明确限制，建议不超过10MB
- **图片尺寸**: 无限制，系统会自动处理

#### 入参示例

```
POST /analyze_image/
Content-Type: multipart/form-data

image: [图片文件]
query: "这张图片中有什么安全隐患？"
```

#### 返回参数

| 参数名    | 参数类型   | 说明                 |
| ------ | ------ | ------------------ |
| status | String | 处理状态：success/error |
| data   | Object | 图片分析结果             |

#### 返回示例

```json
{
  "status": "success",
  "data": {
    "result": "这张图片显示了一个建筑工地场景。我观察到以下安全隐患：\n1. 有工人未佩戴安全帽\n2. 脚手架搭建不规范，缺少安全护栏\n3. 现场堆放的建材没有固定，存在倒塌风险\n4. 电线布置杂乱，可能存在触电危险\n建议立即整改这些安全问题。"
  }
}
```

### 4.2 图片分析页面

#### 接口描述

获取图片分析的Web界面页面。

#### 接口地址

GET /image_analysis

#### 返回内容

返回HTML页面文件，提供图片上传和分析的用户界面。

## 5 视频分析模块

### 5.1 视频分析接口（完整流程）

#### 接口描述

上传视频进行AI分析，采用异步处理模式，自动处理上传、分析和结果获取的完整流程。

#### 接口版本

v1.0

#### 接口地址

POST /analyze_video/

#### 请求参数

| 参数名   | 参数类型   | 是否必须 | 说明                                       |
| ----- | ------ | ---- | ---------------------------------------- |
| video | File   | 是    | 上传的视频文件                                |
| query | String | 否    | 用户对视频的提问内容，不提供时默认为"请分析这段视频"           |

#### 文件限制

- **支持格式**: MP4, AVI, MKV, MOV, WMV, FLV
- **文件大小**: 最大10MB
- **视频时长**: 建议不超过5分钟
- **分辨率**: 无限制，建议1920x1080以下

#### 入参示例

```
POST /analyze_video/
Content-Type: multipart/form-data

video: [视频文件]
query: "这段视频中发生了什么紧急事件？"
```

#### 返回参数

| 参数名          | 参数类型   | 说明                           |
| ------------ | ------ | ---------------------------- |
| status       | String | 处理状态：success/error/timeout |
| data         | Object | 视频分析结果                       |
| task_id      | String | 任务ID（超时时返回，可用于后续查询）         |
| elapsed_time | Number | 处理耗时（秒）                      |

#### 返回示例

**成功响应：**
```json
{
  "status": "success",
  "data": {
    "result": "视频分析结果：这是一段火灾现场的视频。\n主要观察到：\n1. 浓烟滚滚，火势较大\n2. 有消防车辆到达现场\n3. 消防员正在进行灭火作业\n4. 现场有人员疏散\n建议：加强火源控制，确保人员安全疏散。"
  },
  "task_id": "video_task_1234567890",
  "elapsed_time": 45.6
}
```

**超时响应：**
```json
{
  "status": "timeout",
  "message": "视频分析已运行10分钟，可能需要更长时间处理。",
  "task_id": "video_task_1234567890",
  "elapsed_time": 600
}
```

### 5.2 视频上传接口（分步处理）

#### 接口描述

仅上传视频文件获取任务ID，不等待分析结果，适用于需要手动控制处理流程的场景。

#### 接口地址

POST /upload_video/

#### 请求参数

同视频分析接口。

#### 返回示例

```json
{
  "status": "success",
  "task_id": "video_task_1234567890",
  "message": "视频上传成功，正在分析中..."
}
```

### 5.3 视频任务状态查询接口

#### 接口描述

查询视频分析任务的处理状态和结果。

#### 接口地址

POST /video_task_status/

#### 请求参数

| 参数名     | 参数类型   | 是否必须 | 说明              |
| ------- | ------ | ---- | --------------- |
| task_id | String | 是    | 视频分析任务的唯一标识符    |

#### 入参示例

```json
{
  "task_id": "video_task_1234567890"
}
```

#### 返回示例

**分析完成：**
```json
{
  "status": "success",
  "data": {
    "status": "completed",
    "result": "视频分析结果内容..."
  }
}
```

**分析中：**
```json
{
  "status": "success",
  "data": {
    "status": "processing",
    "progress": "正在分析视频内容..."
  }
}
```

### 5.4 视频分析页面

#### 接口描述

获取视频分析的Web界面页面。

#### 接口地址

GET /video_analysis

#### 返回内容

返回HTML页面文件，提供视频上传和分析的用户界面。

## 6 应急知识问答模块

### 6.1 应急知识问答接口

#### 接口描述

专业的应急管理知识问答接口，基于应急管理领域的专业知识库提供准确答案。

#### 接口版本

v1.0

#### 接口地址

POST /emergency_qa/

#### 请求参数

| 参数名             | 参数类型   | 是否必须 | 说明                                  |
| --------------- | ------ | ---- | ----------------------------------- |
| query           | String | 是    | 用户的应急管理相关问题                         |
| conversation_id | String | 否    | 对话会话ID，用于维护多轮对话上下文，不提供时系统自动生成     |

#### 入参示例

```json
{
  "query": "台风来临前企业应该做哪些应急准备？",
  "conversation_id": "emergency_1234567890_123"
}
```

#### 返回参数

| 参数名             | 参数类型   | 说明                      |
| --------------- | ------ | ----------------------- |
| status          | String | 处理状态：success/error    |
| data            | Object | 应急知识问答结果              |
| conversation_id | String | 会话ID，用于后续对话           |

#### 返回示例

```json
{
  "status": "success",
  "data": {
    "answer": "台风来临前企业应做好以下应急准备：\n\n一、人员安全准备：\n1. 制定详细的人员疏散预案\n2. 确定安全避险场所\n3. 建立应急通讯联络机制\n4. 组织员工进行应急演练\n\n二、设施设备保护：\n1. 加固门窗和临时建筑\n2. 清理排水系统，防止积水\n3. 固定户外设备和广告牌\n4. 检查应急发电设备\n\n三、物资储备：\n1. 储备足够的食品和饮用水\n2. 准备应急照明设备\n3. 配备急救医疗用品\n4. 储备防汛沙袋等防护材料\n\n四、信息管理：\n1. 建立应急指挥体系\n2. 保持与气象部门的信息沟通\n3. 制定信息发布机制\n4. 做好重要资料的备份保护"
  },
  "conversation_id": "emergency_1234567890_123"
}
```

### 6.2 应急知识问答页面

#### 接口描述

获取应急知识问答的Web界面页面。

#### 接口地址

GET /emergency_qa

#### 返回内容

返回HTML页面文件，提供专业的应急知识问答界面，包含常见问题快速入口。

## 7 页面路由

### 7.1 首页

#### 接口地址

GET /

#### 描述

获取系统首页，展示平台功能概览和各模块入口。

## 8 技术实现说明

### 8.1 异步处理机制

系统采用异步处理机制处理耗时操作，特别是视频分析：

1. **智能轮询**：动态调整查询间隔（5秒→10秒→30秒）
2. **超时保护**：默认最大等待时间10分钟
3. **错误重试**：连续失败3次后返回错误
4. **任务追踪**：每个任务分配唯一ID，支持后续查询

### 8.2 文件处理

1. **临时文件管理**：上传文件存储在临时目录，处理完成后自动清理
2. **格式验证**：严格验证文件格式和大小
3. **安全处理**：文件名使用UUID避免冲突

### 8.3 会话管理

1. **会话持久化**：支持多轮对话的上下文维护
2. **自动生成**：未提供会话ID时自动生成
3. **格式规范**：会话ID格式为 `{module}_{timestamp}_{random}`

### 8.4 错误处理

1. **分类错误处理**：根据错误类型提供相应的解决建议
2. **详细日志**：记录详细的错误信息用于调试
3. **用户友好**：向用户返回易理解的错误信息

## 9 部署和配置

### 9.1 配置文件

系统配置位于 `config.ini` 文件中，主要配置项：

```ini
[API]
# 对话接口配置
CHAT_URL = https://*************/api/ai_apaas/conversation/runs
CHAT_TIMEOUT = 120

# 图片分析接口配置  
IMAGE_URL = https://*************/dhxx
IMAGE_TIMEOUT = 120

# 视频分析接口配置
VIDEO_UPLOAD_URL = https://*************/api/analysis/video
VIDEO_RESULT_URL = https://*************/api/analysis/video/result
VIDEO_TIMEOUT = 120

# 应急知识问答接口配置
EMERGENCY_QA_URL = https://*************/rpc/2.0/ai_custom/v1/wenxinworkshop/plugin/xx
EMERGENCY_QA_TIMEOUT = 120

# 认证令牌
TOKEN = [认证令牌]

# SSL验证
VERIFY_SSL = false
```

### 9.2 启动命令

```bash
# 进入项目目录
cd jiuan_gpt

# 启动服务
python main.py

# 或使用uvicorn启动
uvicorn main:app --host *********** --port 8088
```

### 9.3 依赖要求

```
fastapi
uvicorn
httpx
python-multipart
```

## 10 更新历史

### v1.0.0 (2024-12-20)

- ✅ 实现基于DeepSeek-r1的智能对话功能
- ✅ 集成图片分析和视频分析功能
- ✅ 新增应急知识问答专业模块
- ✅ 实现异步视频处理和智能轮询机制
- ✅ 提供完整的Web用户界面
- ✅ 支持多轮对话和会话管理
- ✅ 完善的错误处理和用户反馈机制

---

**文档版本**: v1.0  
**最后更新**: 2024-12-20  
**联系方式**: 技术支持团队 