<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>久安大模型 - WebSocket视频分析</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .upload-section h3 {
            margin-top: 0;
            color: #333;
            font-size: 1.3em;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        input[type="file"], input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        input[type="file"]:focus, input[type="text"]:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 500px;
            overflow-y: auto;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .progress-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        .progress-step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: white;
        }
        .step-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            color: white;
        }
        .step-pending {
            background: #6c757d;
        }
        .step-processing {
            background: #ffc107;
            animation: pulse 1.5s infinite;
        }
        .step-completed {
            background: #28a745;
        }
        .step-error {
            background: #dc3545;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .task-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .task-id {
            font-family: monospace;
            background: #f0f0f0;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            margin: 5px 0;
            word-break: break-all;
        }
        .video-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>久安大模型</h1>
            <p>WebSocket视频分析系统</p>
        </div>
        
        <div class="content">
            <!-- 视频上传分析部分 -->
            <div class="upload-section">
                <h3>📹 WebSocket视频分析</h3>
                <div class="form-group">
                    <label for="videoFile">选择视频文件：</label>
                    <input type="file" id="videoFile" accept="video/*" required>
                </div>
                <div class="form-group">
                    <label for="videoQuery">分析问题：</label>
                    <textarea id="videoQuery" placeholder="请输入您想要分析的问题，例如：请分析这段视频的内容"></textarea>
                </div>
                <button class="btn" onclick="analyzeVideo()" id="analyzeBtn">开始分析</button>
                <button class="btn" onclick="cancelAnalysis()" id="cancelBtn" style="display: none; background: #dc3545;">取消分析</button>
                
                <!-- 视频预览 -->
                <div id="videoPreview"></div>
                
                <!-- 分析进度 -->
                <div class="progress-section" id="progressSection">
                    <h4>🔄 分析进度</h4>
                    <div class="progress-step" id="step1">
                        <div class="step-icon step-pending">1</div>
                        <div>获取访问令牌</div>
                    </div>
                    <div class="progress-step" id="step2">
                        <div class="step-icon step-pending">2</div>
                        <div>建立WebSocket连接</div>
                    </div>
                    <div class="progress-step" id="step3">
                        <div class="step-icon step-pending">3</div>
                        <div>上传视频文件</div>
                    </div>
                    <div class="progress-step" id="step4">
                        <div class="step-icon step-pending">4</div>
                        <div>等待分析结果</div>
                    </div>
                </div>
                
                <div class="loading" id="analysisLoading">
                    <div class="spinner"></div>
                    <p id="loadingText">正在分析视频...</p>
                </div>
                
                <div id="analysisResult"></div>
            </div>
        </div>
    </div>

    <script>
        let isAnalyzing = false;
        let currentTaskId = null;

        // 视频文件选择事件
        document.getElementById('videoFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewDiv = document.getElementById('videoPreview');
            
            if (file) {
                // 显示视频预览
                const video = document.createElement('video');
                video.src = URL.createObjectURL(file);
                video.controls = true;
                video.className = 'video-preview';
                
                previewDiv.innerHTML = '';
                previewDiv.appendChild(video);
                
                // 显示文件信息
                const fileInfo = document.createElement('div');
                fileInfo.innerHTML = `
                    <p><strong>文件信息：</strong></p>
                    <p>文件名：${file.name}</p>
                    <p>文件大小：${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p>文件类型：${file.type}</p>
                `;
                previewDiv.appendChild(fileInfo);
            } else {
                previewDiv.innerHTML = '';
            }
        });

        // 分析视频
        async function analyzeVideo() {
            const fileInput = document.getElementById('videoFile');
            const queryInput = document.getElementById('videoQuery');
            const progressSection = document.getElementById('progressSection');
            const loadingDiv = document.getElementById('analysisLoading');
            const resultDiv = document.getElementById('analysisResult');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const cancelBtn = document.getElementById('cancelBtn');

            if (!fileInput.files[0]) {
                showResult(resultDiv, '请选择视频文件', 'error');
                return;
            }

            if (isAnalyzing) {
                showResult(resultDiv, '分析正在进行中，请等待...', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('video', fileInput.files[0]);
            formData.append('query', queryInput.value || '请分析这段视频的内容');

            // 设置分析状态
            isAnalyzing = true;
            analyzeBtn.style.display = 'none';
            cancelBtn.style.display = 'inline-block';
            progressSection.style.display = 'block';
            loadingDiv.style.display = 'block';
            resultDiv.innerHTML = '';

            // 重置进度步骤
            resetProgressSteps();
            updateProgressStep('step1', 'processing');

            try {
                document.getElementById('loadingText').textContent = '正在进行WebSocket视频分析...';
                
                const response = await fetch('/jiuan_analyze_video/', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'session-id': 'websocket_video_session'
                    }
                });

                const data = await response.json();
                
                // 分析完成
                isAnalyzing = false;
                analyzeBtn.style.display = 'inline-block';
                cancelBtn.style.display = 'none';
                loadingDiv.style.display = 'none';

                if (data.status === 'success') {
                    updateProgressStep('step1', 'completed');
                    updateProgressStep('step2', 'completed');
                    updateProgressStep('step3', 'completed');
                    updateProgressStep('step4', 'completed');
                    
                    currentTaskId = data.task_id;
                    
                    let resultText = `✅ 视频分析完成！\n\n`;
                    if (data.task_id) {
                        resultText += `📋 任务ID: ${data.task_id}\n`;
                    }
                    if (data.data && data.data.elapsed_time) {
                        resultText += `⏱️ 分析耗时: ${data.data.elapsed_time.toFixed(2)} 秒\n\n`;
                    }
                    
                    // 显示分析结果
                    if (data.data && data.data.result) {
                        resultText += `📊 分析结果:\n${JSON.stringify(data.data.result, null, 2)}`;
                    } else {
                        resultText += `📊 详细信息:\n${JSON.stringify(data.data, null, 2)}`;
                    }
                    
                    showResult(resultDiv, resultText, 'success');
                    
                    // 显示任务信息
                    if (data.task_id) {
                        const taskInfo = document.createElement('div');
                        taskInfo.className = 'task-info';
                        taskInfo.innerHTML = `
                            <strong>🎯 分析完成</strong><br>
                            任务ID: <span class="task-id">${data.task_id}</span><br>
                            <small>分析已通过WebSocket完成</small>
                        `;
                        resultDiv.appendChild(taskInfo);
                    }
                } else {
                    updateAllStepsError();
                    showResult(resultDiv, `❌ 分析失败: ${data.message}`, 'error');
                }
            } catch (error) {
                isAnalyzing = false;
                analyzeBtn.style.display = 'inline-block';
                cancelBtn.style.display = 'none';
                loadingDiv.style.display = 'none';
                updateAllStepsError();
                showResult(resultDiv, `❌ 请求失败: ${error.message}`, 'error');
            }
        }

        // 取消分析
        function cancelAnalysis() {
            if (isAnalyzing) {
                isAnalyzing = false;
                document.getElementById('analyzeBtn').style.display = 'inline-block';
                document.getElementById('cancelBtn').style.display = 'none';
                document.getElementById('analysisLoading').style.display = 'none';
                document.getElementById('progressSection').style.display = 'none';
                
                showResult(document.getElementById('analysisResult'), '⏹️ 分析已取消', 'warning');
            }
        }

        // 重置进度步骤
        function resetProgressSteps() {
            const steps = ['step1', 'step2', 'step3', 'step4'];
            steps.forEach(stepId => {
                const stepIcon = document.querySelector(`#${stepId} .step-icon`);
                stepIcon.className = 'step-icon step-pending';
            });
        }

        // 更新进度步骤
        function updateProgressStep(stepId, status) {
            const stepIcon = document.querySelector(`#${stepId} .step-icon`);
            stepIcon.className = `step-icon step-${status}`;
        }

        // 所有步骤设为错误状态
        function updateAllStepsError() {
            const steps = ['step1', 'step2', 'step3', 'step4'];
            steps.forEach(stepId => {
                updateProgressStep(stepId, 'error');
            });
        }

        // 显示结果
        function showResult(container, message, type) {
            const existingResults = container.querySelectorAll('.result');
            existingResults.forEach(result => result.remove());
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (isAnalyzing) {
                cancelAnalysis();
            }
        });
    </script>
</body>
</html> 