from typing import Optional, List, Dict, Any
import os
import argparse
import configparser
import logging
import sys

# 添加新的agent模块导入
from src.agent import function_calling_agent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("app")

def main():
    """
    主函数入口
    """
    parser = argparse.ArgumentParser(description="应急管理智能代理CLI")
    parser.add_argument("--query", type=str, help="查询字符串")
    args = parser.parse_args()
    
    if args.query:
        # 使用函数调用代理处理查询
        result = function_calling_agent(args.query)
        if result:
            print(result)
        else:
            print("未找到相关信息，或查询不在支持范围内。")
    else:
        # 交互式模式
        print("欢迎使用应急管理智能代理CLI，输入'exit'退出")
        while True:
            query = input("\n请输入您的问题: ")
            if query.lower() in ['exit', 'quit', '退出']:
                break
            
            # 使用函数调用代理处理查询
            result = function_calling_agent(query)
            if result:
                print("\n回答:", result)
            else:
                print("\n未找到相关信息，或查询不在支持范围内。")

if __name__ == "__main__":
    main() 