"""
应急管理智能代理包

此包包含用于处理应急管理和资源调度的智能代理相关模块
"""

from .api_utils import load_api_config, get_api_url
from .agent_core import create_agent, format_agent_response, filter_think_tags, OutputFilterCallbackHandler
from .api_tools import (
    query_event_surround_resource,
    preview_camera,
    one_map_position_resource,
    event_stop_plan,
    event_start_plan,
    query_all_event_infos,
    search_emergency_plan,
    query_area_plans,
    query_resource_cameras,
    start_call_by_name,
    query_model_resources,
    start_real_time_travel,
    start_meeting,
    end_meeting
)
from .param_extractors import (
    extract_event_params,
    extract_camera_params,
    extract_resource_params,
    extract_plan_params,
    extract_event_query_params,
    extract_area_params,
    extract_resource_camera_params,
    extract_call_params,
    extract_model_name,
    extract_real_time_travel,
    extract_meeting_name
)
from .agent_service import function_calling_agent

__all__ = [
    'function_calling_agent',
    'load_api_config',
    'get_api_url',
    'create_agent',
    'format_agent_response',
    'filter_think_tags',
    'OutputFilterCallbackHandler',
    'query_event_surround_resource',
    'preview_camera',
    'one_map_position_resource',
    'event_stop_plan',
    'event_start_plan',
    'query_all_event_infos',
    'search_emergency_plan',
    'query_area_plans',
    'query_resource_cameras',
    'start_call_by_name',
    'query_model_resources',
    'start_real_time_travel',
    'start_meeting',
    'end_meeting',
    'extract_event_params',
    'extract_camera_params',
    'extract_resource_params',
    'extract_plan_params',
    'extract_event_query_params',
    'extract_area_params',
    'extract_resource_camera_params',
    'extract_call_params',
    'extract_model_name',
    'extract_real_time_travel',
    'extract_meeting_name'
] 