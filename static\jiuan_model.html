<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>久安大模型</title>
  <link rel="stylesheet" href="/static/css/style.css">
  <link rel="stylesheet" href="/static/css/input-fix.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <style>
    .jiuan-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
    }
    .jiuan-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      border-bottom: 1px solid #eaeaea;
    }
    .header-left {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    .header-right {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    .back-btn {
      background-color: transparent;
      border: none;
      color: #666;
      cursor: pointer;
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 5px;
    }
    .back-btn:hover {
      color: #333;
    }
    .mode-selector {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-left: 20px;
    }
    .mode-btn {
      background-color: #f0f0f0;
      color: #333;
      border: none;
      border-radius: 4px;
      padding: 6px 12px;
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s;
    }
    .mode-btn.active {
      background-color: #4e6ef2;
      color: white;
    }
    .mode-btn:hover:not(.active) {
      background-color: #e0e0e0;
    }
    .upload-btn-container {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-left: 20px;
    }
    .upload-button {
      background-color: #4e6ef2;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 6px 12px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 14px;
    }
    .upload-button:hover {
      background-color: #3a5ae8;
    }
    .file-types {
      font-size: 12px;
      color: #888;
      margin-left: 10px;
    }
    .file-error {
      color: #ff5252;
      font-size: 14px;
      position: absolute;
      top: 55px;
      right: 20px;
      background-color: #ffeeee;
      padding: 5px 10px;
      border-radius: 4px;
      border: 1px solid #ffcccc;
      display: none;
      z-index: 1000;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .media-section {
      display: flex;
      align-items: center;
    }
    .media-header {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      white-space: nowrap;
    }
    .media-content {
      display: none;
      position: absolute;
      top: 50px;
      right: 20px;
      width: 300px;
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 5px;
      background-color: white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      z-index: 100;
    }
    .media-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 10px;
      border-bottom: 1px solid #f0f0f0;
    }
    .media-item:last-child {
      border-bottom: none;
    }
    .media-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 13px;
    }
    .media-thumbnail {
      width: 50px;
      height: 50px;
      object-fit: cover;
      border-radius: 4px;
      margin-right: 10px;
    }
    .remove-media-btn {
      background: none;
      border: none;
      color: #ff5252;
      cursor: pointer;
      font-size: 16px;
      padding: 0 5px;
    }
    .media-toggle-button {
      background: none;
      border: none;
      font-size: 14px;
      transition: transform 0.3s;
      padding: 0 5px;
      margin-left: 5px;
    }
    .media-toggle-button.expanded {
      transform: rotate(90deg);
    }
    .welcome-text {
      font-size: 14px;
      font-weight: 500;
    }
    .chat-media {
      max-width: 100%;
      max-height: 300px;
      border-radius: 8px;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    /* WebSocket视频分析状态样式 */
    .video-analysis-status {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 10px 15px;
      border-radius: 8px;
      margin: 10px 0;
      font-size: 13px;
      border-left: 4px solid #fff;
    }

    .video-analysis-info {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 10px 15px;
      margin: 10px 0;
      font-size: 13px;
      color: #495057;
    }

    .video-analysis-error {
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 8px;
      padding: 10px 15px;
      margin: 10px 0;
      color: #721c24;
    }

    .video-analysis-timeout {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 8px;
      padding: 10px 15px;
      margin: 10px 0;
      color: #856404;
    }

    /* 高级设置面板样式 */
    .settings-panel {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .settings-content {
      background: white;
      border-radius: 10px;
      width: 90%;
      max-width: 500px;
      max-height: 80%;
      overflow-y: auto;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .settings-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #eee;
    }

    .settings-header h3 {
      margin: 0;
      color: #333;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .close-btn:hover {
      color: #333;
    }

    .settings-body {
      padding: 20px;
    }

    .setting-group {
      margin-bottom: 20px;
    }

    .setting-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;
    }

    .setting-group select,
    .setting-group input[type="number"] {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 14px;
    }

    .setting-group input[type="range"] {
      width: calc(100% - 60px);
      margin-right: 10px;
    }

    .setting-group span {
      display: inline-block;
      width: 50px;
      text-align: center;
      font-weight: 500;
      color: #666;
    }

    .reset-btn {
      background: #f0f0f0;
      border: 1px solid #ddd;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      color: #333;
    }

    .reset-btn:hover {
      background: #e0e0e0;
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <img src="/static/images/yingji.png" alt="应急指挥产品" class="logo-icon">
          <span class="logo-title">久安大模型</span>
        </div>
      </div>
      <button class="new-chat-btn" id="new-chat-btn">
        <i class="fas fa-plus"></i>
        <span>新建会话</span>
      </button>
      <div class="history-title"></div>
      <div class="history-list" id="history-list">
        <!-- 历史记录将通过JavaScript动态加载 -->
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <div class="jiuan-container">
        <div class="jiuan-header">
          <div class="header-left">
            <button class="back-btn" id="back-btn">
              <i class="fas fa-arrow-left"></i>
              <span>返回主页</span>
            </button>

            <!-- 模式选择器 -->
            <div class="mode-selector">
              <button class="mode-btn active" id="chat-mode-btn">应急知识问答</button>
              <button class="mode-btn" id="image-mode-btn">图片分析</button>
              <button class="mode-btn" id="video-mode-btn">视频分析</button>
              <!-- <button class="mode-btn" id="emergency-mode-btn">应急知识问答</button> -->
              <button class="mode-btn" id="settings-btn" style="margin-left: auto;">⚙️ 高级设置</button>
            </div>

            <!-- 上传按钮 - 初始隐藏 -->
            <div class="upload-btn-container" id="upload-container" style="display: none;">
              <button class="upload-button" id="upload-button">
                <i class="fas fa-cloud-upload-alt"></i>
                <span id="upload-text">上传文件</span>
              </button>
              <input type="file" id="file-upload" style="display: none;">
            </div>

            <!-- 媒体显示区域 -->
            <div class="media-section" id="media-section" style="display: none;">
              <div class="media-header" id="media-header">
                <span id="media-header-text">已上传文件</span>
                <button class="media-toggle-button" id="media-toggle-button">▶</button>
              </div>
              <div class="media-content" id="media-content" style="display: block;">
                <!-- 已选择的媒体将在这里显示 -->
              </div>
            </div>
          </div>

          <div class="header-right">
            <span class="file-error" id="file-error"></span>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-container">
          <div class="chat-history" id="chat-history">
            <!-- 聊天记录将通过JavaScript动态加载 -->
            <div class="message ai-message">
              <div class="message-avatar">
                <img src="/static/images/yingji.png" alt="AI">
              </div>
              <div class="message-content">
                您好，我是久安大模型助手，请选择对话模式并开始提问。
              </div>
            </div>
          </div>
          <div class="input-container">
            <textarea class="message-input" id="message-input" placeholder="输入问题..." rows="1"></textarea>
            <button class="send-btn" id="send-btn">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 高级设置面板 -->
  <div id="settings-panel" class="settings-panel" style="display: none;">
    <div class="settings-content">
      <div class="settings-header">
        <h3>高级设置</h3>
        <button id="close-settings" class="close-btn">×</button>
      </div>
      <div class="settings-body">
        <div class="setting-group">
          <label for="model-select">模型选择:</label>
          <select id="model-select">
            <option value="deepseek-r1-full">deepseek-r1-full</option>
            <option value="deepseek-chat">deepseek-chat</option>
            <option value="gpt-3.5-turbo">文心一言</option>
          </select>
        </div>

        <div class="setting-group">
          <button id="reset-settings" class="reset-btn">重置为默认值</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化marked选项
      marked.setOptions({
        breaks: true,  // 支持GitHub风格的换行
        gfm: true      // 支持GitHub风格的Markdown
      });

      // 全局变量
      let currentMode = 'chat'; // 默认为文本对话模式
      let uploadedMedia = null; // 存储已上传的媒体文件
      let advancedSettings = {
        model: 'deepseek-r1-full'
      };

      // DOM元素
      const chatModeBtn = document.getElementById('chat-mode-btn');
      const imageModeBtn = document.getElementById('image-mode-btn');
      const videoModeBtn = document.getElementById('video-mode-btn');
      const emergencyModeBtn = document.getElementById('emergency-mode-btn');
      const uploadContainer = document.getElementById('upload-container');
      const uploadButton = document.getElementById('upload-button');
      const uploadText = document.getElementById('upload-text');
      const fileUpload = document.getElementById('file-upload');
      const mediaSection = document.getElementById('media-section');
      const mediaHeader = document.getElementById('media-header');
      const mediaHeaderText = document.getElementById('media-header-text');
      const mediaContent = document.getElementById('media-content');
      const mediaToggleButton = document.getElementById('media-toggle-button');
      const fileError = document.getElementById('file-error');
      const messageInput = document.getElementById('message-input');
      const sendBtn = document.getElementById('send-btn');

      // 高级设置相关元素
      const settingsBtn = document.getElementById('settings-btn');
      const settingsPanel = document.getElementById('settings-panel');
      const closeSettingsBtn = document.getElementById('close-settings');
      const modelSelect = document.getElementById('model-select');
      const resetSettingsBtn = document.getElementById('reset-settings');

      // 返回主页按钮
      document.getElementById('back-btn').addEventListener('click', function() {
        window.location.href = '/';
      });

      // 新建会话按钮
      document.getElementById('new-chat-btn').addEventListener('click', function() {
        clearChatHistory();
        clearUploadedMedia();
      });

      // 模式切换按钮事件
      chatModeBtn.addEventListener('click', function() {
        setMode('chat');
      });

      imageModeBtn.addEventListener('click', function() {
        setMode('image');
      });

      videoModeBtn.addEventListener('click', function() {
        setMode('video');
      });

      // emergencyModeBtn.addEventListener('click', function() {
      //   setMode('emergency');
      // });

      // 高级设置按钮事件
      settingsBtn.addEventListener('click', function() {
        settingsPanel.style.display = 'flex';
        updateSettingsUI();
      });

      closeSettingsBtn.addEventListener('click', function() {
        settingsPanel.style.display = 'none';
      });

      // 点击面板外部关闭设置
      settingsPanel.addEventListener('click', function(e) {
        if (e.target === settingsPanel) {
          settingsPanel.style.display = 'none';
        }
      });

      // 设置项变化事件
      modelSelect.addEventListener('change', function() {
        advancedSettings.model = this.value;
      });

      resetSettingsBtn.addEventListener('click', function() {
        resetAdvancedSettings();
      });

      // 设置当前模式
      function setMode(mode) {
        currentMode = mode;

        // 更新按钮状态
        chatModeBtn.classList.toggle('active', mode === 'chat');
        imageModeBtn.classList.toggle('active', mode === 'image');
        videoModeBtn.classList.toggle('active', mode === 'video');
        // emergencyModeBtn.classList.toggle('active', mode === 'emergency');

        // 清除已上传的媒体
        clearUploadedMedia();

        // 根据模式显示/隐藏上传按钮
        if (mode === 'chat' || mode === 'emergency') {
          uploadContainer.style.display = 'none';
          if (mode === 'chat') {
            messageInput.placeholder = "输入问题...";
          } else if (mode === 'emergency') {
            messageInput.placeholder = "请输入应急行业相关问题...";
          }
        } else {
          uploadContainer.style.display = 'flex';

          // 设置上传按钮文本和文件类型
          if (mode === 'image') {
            uploadText.textContent = '上传图片';
            fileUpload.accept = 'image/*';
            messageInput.placeholder = "输入问题或直接发送分析图片...";
            mediaHeaderText.textContent = '已上传图片';
          } else if (mode === 'video') {
            uploadText.textContent = '上传视频';
            fileUpload.accept = 'video/*';
            messageInput.placeholder = "输入问题或直接发送分析视频...";
            mediaHeaderText.textContent = '已上传视频';
          }
        }
      }

      // 点击上传按钮触发文件选择
      uploadButton.addEventListener('click', function() {
        fileUpload.click();
      });

      // 媒体列表折叠/展开功能
      mediaHeader.addEventListener('click', function() {
        if (mediaContent.style.display === 'block') {
          mediaContent.style.display = 'none';
          mediaToggleButton.classList.remove('expanded');
        } else {
          mediaContent.style.display = 'block';
          mediaToggleButton.classList.add('expanded');
        }
      });

      // 文件选择处理
      fileUpload.addEventListener('change', function() {
        if (currentMode === 'image') {
          handleImageSelection(this.files);
        } else if (currentMode === 'video') {
          handleVideoSelection(this.files);
        }
        this.value = ''; // 清空文件输入，允许重复选择相同文件
      });

      // 处理图片选择
      function handleImageSelection(files) {
        if (files.length === 0) return;

        // 只处理第一个文件
        const file = files[0];

        // 检查文件类型
        if (!file.type.startsWith('image/')) {
          showFileError('请上传图片文件');
          return;
        }

        // 清除之前的媒体
        clearUploadedMedia();

        // 保存新上传的媒体
        uploadedMedia = file;

        // 更新媒体显示
        refreshMediaDisplay();

        // 显示媒体区域
        mediaSection.style.display = 'block';
        // 默认展开媒体列表
        mediaContent.style.display = 'block';
        mediaToggleButton.classList.add('expanded');
      }

      // 处理视频选择
      function handleVideoSelection(files) {
        if (files.length === 0) return;

        // 只处理第一个文件
        const file = files[0];

        // 检查文件类型
        if (!file.type.startsWith('video/')) {
          showFileError('请上传视频文件');
          return;
        }

        // 清除之前的媒体
        clearUploadedMedia();

        // 保存新上传的媒体
        uploadedMedia = file;

        // 更新媒体显示
        refreshMediaDisplay();

        // 显示媒体区域
        mediaSection.style.display = 'block';
        // 默认展开媒体列表
        mediaContent.style.display = 'block';
        mediaToggleButton.classList.add('expanded');
      }

      // 显示文件错误
      function showFileError(message) {
        fileError.textContent = message;
        fileError.style.display = 'block';

        // 3秒后自动隐藏错误
        setTimeout(() => {
          fileError.style.display = 'none';
        }, 3000);

        // 点击错误消息可以关闭
        fileError.addEventListener('click', function() {
          this.style.display = 'none';
        });
      }

      // 添加媒体到显示区域
      function refreshMediaDisplay() {
        mediaContent.innerHTML = '';

        if (uploadedMedia) {
          const mediaItem = document.createElement('div');
          mediaItem.className = 'media-item';

          // 创建缩略图
          const thumbnail = document.createElement('img');
          thumbnail.className = 'media-thumbnail';

          if (currentMode === 'image') {
            thumbnail.src = URL.createObjectURL(uploadedMedia);
          } else if (currentMode === 'video') {
            // 为视频创建缩略图
            const video = document.createElement('video');
            video.src = URL.createObjectURL(uploadedMedia);
            video.addEventListener('loadeddata', function() {
              // 视频加载后，捕获第一帧作为缩略图
              const canvas = document.createElement('canvas');
              canvas.width = 100;
              canvas.height = 100;
              const ctx = canvas.getContext('2d');
              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
              thumbnail.src = canvas.toDataURL();
            });
            video.load();
          }

          // 创建媒体名称
          const mediaName = document.createElement('div');
          mediaName.className = 'media-name';
          mediaName.textContent = uploadedMedia.name;
          mediaName.title = uploadedMedia.name;

          // 创建删除按钮
          const removeBtn = document.createElement('button');
          removeBtn.className = 'remove-media-btn';
          removeBtn.textContent = '×';
          removeBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 防止点击事件传递
            clearUploadedMedia();
          });

          // 组装元素
          mediaItem.appendChild(thumbnail);
          mediaItem.appendChild(mediaName);
          mediaItem.appendChild(removeBtn);
          mediaContent.appendChild(mediaItem);
        }
      }

      // 清空已上传的媒体
      function clearUploadedMedia() {
        uploadedMedia = null;
        mediaContent.innerHTML = '';
        mediaSection.style.display = 'none';
      }

      // 更新设置UI
      function updateSettingsUI() {
        modelSelect.value = advancedSettings.model;
      }

      // 重置高级设置
      function resetAdvancedSettings() {
        advancedSettings = {
          model: 'deepseek-r1-full'
        };
        updateSettingsUI();
      }

      // 发送按钮和回车键发送
      sendBtn.addEventListener('click', sendMessage);
      messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendMessage();
        }
      });

      // 清空聊天记录
      function clearChatHistory() {
        const chatHistory = document.getElementById('chat-history');
        chatHistory.innerHTML = `
          <div class="message ai-message">
            <div class="message-avatar">
              <img src="/static/images/yingji.png" alt="AI">
            </div>
            <div class="message-content">
              您好，我是久安大模型助手，请选择对话模式并开始提问。
            </div>
          </div>
        `;
      }

      // 发送消息函数
      function sendMessage() {
        const message = messageInput.value.trim();

        // 检查是否需要上传媒体
        if ((currentMode === 'image' || currentMode === 'video') && !uploadedMedia) {
          showFileError(`请先上传${currentMode === 'image' ? '图片' : '视频'}再提问`);
          return;
        }

        // 清空输入框
        messageInput.value = '';

        // 添加用户消息到聊天历史
        const chatHistory = document.getElementById('chat-history');
        const userMsg = document.createElement('div');
        userMsg.className = 'message user-message';

        // 创建用户头像
        const userAvatar = document.createElement('div');
        userAvatar.className = 'message-avatar';
        userAvatar.innerHTML = '<i class="fas fa-user"></i>';

        // 创建用户消息内容
        const userContent = document.createElement('div');
        userContent.className = 'message-content';

        // 设置默认消息
        let defaultMessage = '';
        if (currentMode === 'image') {
          defaultMessage = '请分析这张图片';
        } else if (currentMode === 'video') {
          defaultMessage = '请分析这段视频';
        }

        userContent.textContent = message || defaultMessage;

        // 组装用户消息
        userMsg.appendChild(userAvatar);
        userMsg.appendChild(userContent);
        chatHistory.appendChild(userMsg);

        // 添加AI消息占位符
        const aiMsg = document.createElement('div');
        aiMsg.className = 'message ai-message';

        // 创建AI头像
        const aiAvatar = document.createElement('div');
        aiAvatar.className = 'message-avatar';
        aiAvatar.innerHTML = '<img src="/static/images/yingji.png" alt="AI">';

        // 创建消息内容
        const aiContent = document.createElement('div');
        aiContent.className = 'message-content';
        aiContent.innerHTML = `
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        `;

        // 组装AI消息
        aiMsg.appendChild(aiAvatar);
        aiMsg.appendChild(aiContent);
        chatHistory.appendChild(aiMsg);

        // 滚动到底部
        chatHistory.scrollTop = chatHistory.scrollHeight;

        // 根据当前模式调用相应的API
        if (currentMode === 'chat') {
          chatWithModel(message, aiMsg);
        } else if (currentMode === 'image') {
          analyzeImage(message || defaultMessage, aiMsg);
        } else if (currentMode === 'video') {
          analyzeVideo(message || defaultMessage, aiMsg);
        } else if (currentMode === 'emergency') {
          emergencyKnowledgeQA(message, aiMsg);
        }
      }

      // 调用文本对话API
      async function chatWithModel(message, aiMsgElement) {
        try {
          // 准备请求数据 - 简化为只传query参数
          const requestData = {
            query: message
          };

          // 发送请求
          const response = await fetch('/jiuan_chat/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
          });

          if (!response.ok) {
            throw new Error('网络请求失败');
          }

          // 解析响应
          const responseData = await response.json();

          // 添加调试信息
          console.log('图片分析响应数据:', responseData);
          console.log('responseData.status:', responseData.status);
          console.log('responseData.data:', responseData.data);
          console.log('responseData.result:', responseData.result);

          // 更新AI消息内容
          const messageContent = aiMsgElement.querySelector('.message-content');

          if (responseData.status === 'success') {
            // 检查是否是函数调用结果
            if (responseData.data && responseData.data.is_function_call) {
              messageContent.innerHTML = marked.parse(responseData.data.answer || responseData.data.result);
            } else {
              // 正常对话结果 - 支持久安大模型的响应格式
              // 优先从data.answer获取，然后从data.result.msg获取，最后尝试其他字段
              const result = responseData.data.answer || 
                           responseData.data.result?.msg ||
                           responseData.data.result || 
                           responseData.data.object?.choices?.[0]?.message?.content || 
                           '无法获取回复内容';
              messageContent.innerHTML = marked.parse(result);
            }
          } else {
            messageContent.textContent = responseData.message || '获取回答失败';
          }

          // 滚动到底部
          const chatHistory = document.getElementById('chat-history');
          chatHistory.scrollTop = chatHistory.scrollHeight;

        } catch (error) {
          console.error('获取回答时出错:', error);
          aiMsgElement.querySelector('.message-content').textContent = '获取回答时出错，请稍后重试';
        }
      }

      // 获取对话历史记录
      function getChatHistory() {
        const chatHistory = document.getElementById('chat-history');
        const messages = chatHistory.querySelectorAll('.message');
        const history = [];

        messages.forEach(message => {
          const content = message.querySelector('.message-content');
          if (content && !content.querySelector('.typing-indicator')) {
            const isUser = message.classList.contains('user-message');
            const text = content.textContent.trim();
            if (text && text !== '您好，我是久安大模型助手，请选择对话模式并开始提问。') {
              history.push({
                role: isUser ? 'user' : 'assistant',
                content: text
              });
            }
          }
        });

        return history;
      }

      // 调用图片分析API
      async function analyzeImage(query, aiMsgElement) {
        try {
          // 获取对话历史
          const history = getChatHistory();

          // 准备上传图片
          const formData = new FormData();
          formData.append('image', uploadedMedia);
          formData.append('query', query);
          formData.append('history', JSON.stringify(history));

          // 发送分析请求
          const response = await fetch('/jiuan_analyze_image/', {
            method: 'POST',
            body: formData
          });

          if (!response.ok) {
            throw new Error('网络请求失败');
          }

          // 解析响应
          const responseData = await response.json();
          
          // 添加调试信息
          console.log('图片分析响应数据:', responseData);
          console.log('responseData.status:', responseData.status);
          console.log('responseData.data:', responseData.data);
          console.log('responseData.result:', responseData.result);

          // 更新AI消息内容
          const messageContent = aiMsgElement.querySelector('.message-content');

          // 修复：正确处理久安大模型API的响应格式
          if (responseData.status === 200 || responseData.status === 'success') {
            // 优先从result.msg获取内容，这是久安大模型API的标准格式
            const result = responseData.result?.msg || 
                          responseData.data?.result?.msg ||
                          responseData.data?.result || 
                          responseData.result || 
                          '无法获取分析结果';
            messageContent.innerHTML = marked.parse(result);
          } else {
            messageContent.textContent = responseData.message || '图片分析失败';
          }

          // 滚动到底部
          const chatHistory = document.getElementById('chat-history');
          chatHistory.scrollTop = chatHistory.scrollHeight;

        } catch (error) {
          console.error('获取回答时出错:', error);
          aiMsgElement.querySelector('.message-content').textContent = '获取回答时出错，请稍后重试';
        }
      }

      // 调用视频分析API（基于WebSocket）
      async function analyzeVideo(query, aiMsgElement) {
        try {
          // 获取对话历史
          const history = getChatHistory();

          // 准备上传视频
          const formData = new FormData();
          formData.append('video', uploadedMedia);
          formData.append('query', query);
          // 注：WebSocket模式下不使用history参数，简化流程
          // formData.append('history', JSON.stringify(history));

          // 更新AI消息内容为正在分析状态
          const messageContent = aiMsgElement.querySelector('.message-content');
          messageContent.innerHTML = `
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div class="video-analysis-status">
              🔄 正在进行视频分析，请稍候...<br>
            </div>
          `;

          // 发送WebSocket视频分析请求
          const response = await fetch('/jiuan_analyze_video/', {
            method: 'POST',
            body: formData,
            headers: {
              'session-id': 'jiuan_model_video_session'
            }
          });

          if (!response.ok) {
            throw new Error('网络请求失败');
          }

          // 解析响应
          const responseData = await response.json();

          // 添加调试信息
          console.log('WebSocket视频分析响应数据:', responseData);
          console.log('responseData.status:', responseData.status);
          console.log('responseData.data:', responseData.data);
          
          // 更新AI消息内容
          if (responseData.status === 'success') {
            let result = '';
            
            // 处理新的WebSocket视频分析响应格式
            if (responseData.data && responseData.data.result) {
              // 从新格式中获取描述信息
              result = responseData.data.result.describe || '视频分析完成，但未获取到具体结果';
            } else if (responseData.result) {
              // 兼容旧格式
              const resultData = responseData.result;
              result = resultData.describe || 
                      resultData.msg || 
                      resultData.response || 
                      resultData.answer ||
                      resultData.text ||
                      resultData.content ||
                      (typeof resultData === 'string' ? resultData : '');
              
              // 如果result还是对象，尝试序列化为可读格式
              if (!result && typeof resultData === 'object') {
                try {
                  result = JSON.stringify(resultData, null, 2);
                } catch (e) {
                  result = String(resultData);
                }
              }
            } else {
              // 兜底处理
              result = responseData.message || '视频分析完成，但未获取到具体结果';
            }

            // 确保result是字符串
            if (typeof result !== 'string') {
              try {
                result = JSON.stringify(result, null, 2);
              } catch (e) {
                result = String(result || '视频分析完成，但未获取到具体结果');
              }
            }

            // 如果结果为空，设置默认值
            if (!result || result.trim() === '') {
              result = '视频分析完成，但未获取到具体结果';
            }

            // 直接显示分析结果
            messageContent.innerHTML = marked.parse(result);
                     } else if (responseData.status === 'timeout') {
             const taskId = responseData.data?.result?.taskId || responseData.task_id;
             messageContent.innerHTML = `
               <div class="video-analysis-timeout">
                 ⏰ <strong>视频分析超时</strong><br>
                 ${responseData.message || '分析时间较长，请稍后重试'}
                 ${taskId ? `<br><br>📋 任务ID: ${taskId}` : ''}
               </div>
             `;
           } else {
             messageContent.innerHTML = `
               <div class="video-analysis-error">
                 ❌ <strong>视频分析失败</strong><br>
                 ${responseData.message || '未知错误'}
               </div>
             `;
          }

          // 滚动到底部
          const chatHistory = document.getElementById('chat-history');
          chatHistory.scrollTop = chatHistory.scrollHeight;

        } catch (error) {
          console.error('WebSocket视频分析出错:', error);
          const messageContent = aiMsgElement.querySelector('.message-content');
          messageContent.innerHTML = `
            <div class="video-analysis-error">
              ❌ <strong>网络请求失败</strong><br>
              ${error.message}<br>
              <small>请检查网络连接或稍后重试</small>
            </div>
          `;
        }
      }

      // 调用应急知识问答API
      async function emergencyKnowledgeQA(message, aiMsgElement) {
        try {
          // 准备请求数据
          const requestData = {
            message: message
          };

          // 发送请求
          const response = await fetch('/jiuan_emergency_knowledge/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
          });

          if (!response.ok) {
            throw new Error('网络请求失败');
          }

          // 解析响应
          const responseData = await response.json();

          // 添加调试信息
          console.log('应急知识问答响应数据:', responseData);
          console.log('responseData.status:', responseData.status);
          console.log('responseData.data:', responseData.data);
          console.log('responseData.result:', responseData.result);

          // 更新AI消息内容
          const messageContent = aiMsgElement.querySelector('.message-content');

          if (responseData.status === 'success') {
            // 检查是否是函数调用结果
            if (responseData.data && responseData.data.is_function_call) {
              messageContent.innerHTML = marked.parse(responseData.data.result);
            } else {
              // 正常应急知识问答结果 - 支持久安大模型的响应格式
              // 优先从data.answer获取，然后从data.result.msg获取，最后尝试其他字段
              const result = responseData.data.answer || 
                           responseData.data.result?.msg ||
                           responseData.data.result || 
                           responseData.data.response || 
                           '无法获取回复内容';
              messageContent.innerHTML = marked.parse(result);
            }
          } else {
            messageContent.textContent = responseData.message || '应急知识问答失败';
          }

          // 滚动到底部
          const chatHistory = document.getElementById('chat-history');
          chatHistory.scrollTop = chatHistory.scrollHeight;

        } catch (error) {
          console.error('获取回答时出错:', error);
          aiMsgElement.querySelector('.message-content').textContent = '获取回答时出错，请稍后重试';
        }
      }
    });
  </script>
</body>
</html>
