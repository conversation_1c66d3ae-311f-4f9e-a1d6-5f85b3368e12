<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能对话 - 应急管理大模型平台</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        header {
            background-color: #1e3a8a;
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .nav-menu {
            display: flex;
        }
        
        .nav-menu a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-menu a:hover {
            background-color: #2d4eaf;
        }
        
        .nav-menu a.active {
            background-color: #2d4eaf;
        }
        
        main {
            flex: 1;
            display: flex;
            padding: 20px 0;
        }
        
        .chat-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 180px);
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.bot {
            justify-content: flex-start;
        }
        
        .message-content {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .user .message-content {
            background-color: #1e3a8a;
            color: white;
            border-top-right-radius: 0;
        }
        
        .bot .message-content {
            background-color: #f0f5ff;
            color: #333;
            border-top-left-radius: 0;
        }
        
        .chat-input {
            display: flex;
            padding: 15px;
            background-color: #f9f9f9;
            border-top: 1px solid #eee;
        }
        
        .chat-input textarea {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            height: 24px;
            max-height: 120px;
            transition: height 0.2s;
        }
        
        .chat-input textarea:focus {
            outline: none;
            border-color: #1e3a8a;
        }
        
        .chat-input button {
            margin-left: 10px;
            padding: 0 20px;
            background-color: #1e3a8a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .chat-input button:hover {
            background-color: #2d4eaf;
        }
        
        .chat-input button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        footer {
            text-align: center;
            padding: 20px;
            background-color: #1e3a8a;
            color: #ccc;
        }
        
        .typing-indicator {
            display: inline-block;
            padding: 10px 15px;
            background-color: #f0f5ff;
            border-radius: 8px;
            margin-bottom: 20px;
            color: #666;
        }
        
        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #1e3a8a;
            border-radius: 50%;
            margin-right: 5px;
            animation: typing 1s infinite;
        }
        
        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-5px); }
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-content">
            <div class="logo">应急管理大模型平台</div>
            <div class="nav-menu">
                <a href="/">首页</a>
                <a href="/chat" class="active">智能对话</a>
                <a href="/emergency_qa">应急知识问答</a>
                <a href="/image_analysis">图片分析</a>
                <a href="/video_analysis">视频分析</a>
            </div>
        </div>
    </header>
    
    <main class="container">
        <div class="chat-container">
            <div id="chat-messages" class="chat-messages">
                <div class="message bot">
                    <div class="message-content">
                        您好，我是应急管理大模型助手，请问有什么可以帮您？
                    </div>
                </div>
            </div>
            
            <div class="chat-input">
                <textarea id="message-input" placeholder="请输入您的问题..." rows="1"></textarea>
                <button id="send-button">发送</button>
            </div>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>© 2024 应急管理大模型平台 版权所有</p>
        </div>
    </footer>
    
    <script>
        // 生成唯一会话ID
        const sessionId = 'chat_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
        let conversationId = null; // 保存conversation_id
        
        // 获取DOM元素
        const chatMessages = document.getElementById('chat-messages');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        
        // 自动调整文本区域高度
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
        
        // 按Enter发送消息（Shift+Enter换行）
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 点击发送按钮
        sendButton.addEventListener('click', sendMessage);
        
        // 发送消息函数
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // 添加用户消息到聊天界面
            addMessage(message, 'user');
            
            // 清空输入框
            messageInput.value = '';
            messageInput.style.height = 'auto';
            
            // 显示机器人正在输入
            const typingIndicator = document.createElement('div');
            typingIndicator.className = 'typing-indicator';
            typingIndicator.innerHTML = '<span></span><span></span><span></span> 正在思考...';
            chatMessages.appendChild(typingIndicator);
            
            // 自动滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            // 禁用发送按钮
            sendButton.disabled = true;
            
            // 准备请求体，包含conversation_id
            const requestBody = { 
                message: message
            };
            
            // 如果有conversation_id，添加到请求中
            if (conversationId) {
                requestBody.conversation_id = conversationId;
            }
            
            // 发送API请求
            fetch('/chat/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'session-id': sessionId
                },
                body: JSON.stringify(requestBody)
            })
            .then(response => response.json())
            .then(data => {
                // 移除输入指示器
                chatMessages.removeChild(typingIndicator);
                
                // 处理响应
                if (data.status === 'success' && data.data) {
                    // 保存conversation_id以便后续使用
                    if (data.conversation_id) {
                        conversationId = data.conversation_id;
                    }
                    
                    // 提取回复文本
                    let botReply = "对不起，我没有获取到回复内容。";
                    
                    // 根据新的API返回格式提取回复
                    if (data.data.response) {
                        botReply = data.data.response;
                    } else if (data.data.result) {
                        botReply = data.data.result;
                    } else if (data.data.msg) {
                        botReply = data.data.msg;
                    } else if (data.data.content) {
                        botReply = data.data.content;
                    }
                    
                    // 添加机器人回复
                    addMessage(botReply, 'bot');
                } else {
                    // 处理错误
                    addMessage('抱歉，发生了错误: ' + (data.message || '未知错误'), 'bot');
                }
                
                // 启用发送按钮
                sendButton.disabled = false;
            })
            .catch(error => {
                // 移除输入指示器
                chatMessages.removeChild(typingIndicator);
                
                // 添加错误消息
                addMessage('网络错误，请稍后重试: ' + error.message, 'bot');
                
                // 启用发送按钮
                sendButton.disabled = false;
            });
        }
        
        // 添加消息到聊天界面
        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + sender;
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            
            // 处理换行符
            messageContent.innerText = text;
            
            messageDiv.appendChild(messageContent);
            chatMessages.appendChild(messageDiv);
            
            // 自动滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    </script>
</body>
</html> 