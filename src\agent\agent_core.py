"""
代理核心模块 - 包含代理创建和输出处理功能
"""

import os
import re
import json
import configparser
import logging
from langchain_community.chat_models import ChatOllama
from langchain_openai import ChatOpenAI
from langchain.agents import create_react_agent, AgentExecutor
from langchain_core.prompts import PromptTemplate
from langchain_core.callbacks.base import BaseCallbackHandler

from .api_tools import (
    query_event_surround_resource,
    preview_camera,
    one_map_position_resource,
    event_stop_plan,
    event_start_plan,
    query_all_event_infos,
    search_emergency_plan,
    query_area_plans,
    query_resource_cameras,
    start_call_by_name,
    query_model_resources,
    start_real_time_travel,
    start_meeting,
    end_meeting
)

# 创建logger
logger = logging.getLogger("langchain_agent")

# 创建自定义的ReAct提示词模板
REACT_CUSTOM_PROMPT = """你是一个专门解答应急管理和资源调度问题的智能助手。请根据用户的问题，直接调用合适的工具并返回结果。

Question: {input}

可用工具:
{tools}

请按以下格式回复：
Action: 要调用的工具名称（必须是下面这些之一: {tool_names}）
Action Input: 输入参数

Observation: 工具返回的结果

Final Answer: 直接返回格式化后的结果，无需额外解释

{agent_scratchpad}"""

# 过滤模型输出中的<think>标签和内容
def filter_think_tags(text):
    """
    过滤掉模型输出中的<think>...</think>标签及其内容
    """
    if not text:
        return text
    filtered_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    return filtered_text

# 创建一个自定义回调处理器来过滤输出
class OutputFilterCallbackHandler(BaseCallbackHandler):
    """过滤模型输出的回调处理器"""
    
    def on_llm_new_token(self, token: str, **kwargs) -> None:
        """流式输出时过滤token"""
        pass
    
    def on_llm_end(self, response, **kwargs) -> None:
        """当LLM完成生成时过滤输出"""
        if hasattr(response, 'generations') and response.generations:
            for gen_list in response.generations:
                for gen in gen_list:
                    if hasattr(gen, 'text'):
                        gen.text = filter_think_tags(gen.text)
                    # 处理message类型的输出
                    if hasattr(gen, 'message') and hasattr(gen.message, 'content'):
                        gen.message.content = filter_think_tags(gen.message.content)
    
    def on_chain_end(self, outputs, **kwargs) -> None:
        """当Chain完成时过滤输出"""
        if 'output' in outputs and isinstance(outputs['output'], str):
            outputs['output'] = filter_think_tags(outputs['output'])


class CustomFunctionCallingAgent:
    """自定义Function Calling代理，使用自定义提示词格式"""

    def __init__(self, llm, functions, output_filter):
        self.llm = llm
        self.functions = functions
        self.output_filter = output_filter

        # 创建函数名到函数的映射
        self.function_map = {
            "query_event_surround_resource": query_event_surround_resource,
            "preview_camera": preview_camera,
            "one_map_position_resource": one_map_position_resource,
            "event_stop_plan": event_stop_plan,
            "event_start_plan": event_start_plan,
            "query_all_event_infos": query_all_event_infos,
            "search_emergency_plan": search_emergency_plan,
            "query_area_plans": query_area_plans,
            "query_resource_cameras": query_resource_cameras,
            "start_call_by_name": start_call_by_name,
            "query_model_resources": query_model_resources,
            "start_real_time_travel": start_real_time_travel,
            "start_meeting": start_meeting,
            "end_meeting": end_meeting
        }

    def _create_function_calling_prompt(self, user_query: str) -> str:
        """创建函数调用提示词（参考jiuan_agent格式）"""
        # 简化的函数列表JSON
        functions_json = json.dumps(self.functions, ensure_ascii=False)

        # 使用与jiuan_agent相同的提示词格式
        prompt = f"你是一个只能调用函数的助手，必须根据用户的问题选择一个函数，并填写参数。你的回复必须严格按照以下格式返回，不能输出任何自然语言或解释说明： 你只能输出如下格式： {{ \"function_call\": {{ \"name\": \"函数名称\", \"arguments\": \"参数 JSON 字符串\" }} }} 可用函数列表： {functions_json} 用户问题：{user_query}"

        return prompt

    def _parse_function_call(self, response: str) -> dict:
        """解析函数调用响应"""
        try:
            # 清理响应文本
            response = response.strip()

            # 尝试直接解析JSON
            if response.startswith('{') and response.endswith('}'):
                return json.loads(response)

            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())

            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {str(e)}, 响应: {response}")
            return None
        except Exception as e:
            logger.error(f"解析函数调用时出错: {str(e)}")
            return None

    def _execute_function(self, function_call: dict) -> str:
        """执行函数调用"""
        try:
            function_name = function_call.get("function_call", {}).get("name")
            arguments_str = function_call.get("function_call", {}).get("arguments", "{}")

            if not function_name:
                return "错误：未找到函数名称"

            # 解析参数
            try:
                arguments = json.loads(arguments_str) if isinstance(arguments_str, str) else arguments_str
            except json.JSONDecodeError:
                return f"错误：参数解析失败 - {arguments_str}"

            if function_name not in self.function_map:
                return f"错误：未知函数 - {function_name}"

            # 根据函数名调用对应的函数
            func = self.function_map[function_name]

            # 将参数转换为函数调用格式
            if function_name == "query_event_surround_resource":
                event_name = arguments.get("event_name", "")
                distance = arguments.get("distance", 5)
                resource_type = arguments.get("resource_type", "")
                param = f"{event_name},{distance}"
                if resource_type:
                    param += f",{resource_type}"
                return func(param)
            elif function_name in ["preview_camera"]:
                camera_id = arguments.get("camera_id", "")
                return func(camera_id)
            elif function_name in ["one_map_position_resource"]:
                resource_name = arguments.get("resource_name", "")
                return func(f"{resource_name},")
            elif function_name in ["event_stop_plan", "event_start_plan"]:
                event_name = arguments.get("event_name", "")
                return func(event_name)
            elif function_name == "query_all_event_infos":
                page = arguments.get("page", 1)
                page_size = arguments.get("page_size", 50)
                return func(f"{page},{page_size},,")
            elif function_name == "search_emergency_plan":
                page = arguments.get("page", 1)
                page_size = arguments.get("page_size", 20)
                return func(f",{page},{page_size}")
            elif function_name == "query_area_plans":
                area_name = arguments.get("area_name", "")
                return func(area_name)
            elif function_name == "query_resource_cameras":
                resource_name = arguments.get("resource_name", "")
                resource_type = arguments.get("resource_type", "")
                return func(f"{resource_name},{resource_type}")
            elif function_name == "start_call_by_name":
                resource_name = arguments.get("resource_name", "")
                resource_type = arguments.get("resource_type", "")
                calling_type = arguments.get("calling_type", "0")
                return func(f"{resource_name},{resource_type},{calling_type}")
            elif function_name == "query_model_resources":
                model_name = arguments.get("model_name", "")
                return func(model_name)
            elif function_name == "start_real_time_travel":
                event_name = arguments.get("event_name", "")
                return func(event_name) if event_name else func()
            elif function_name in ["start_meeting", "end_meeting"]:
                meeting_name = arguments.get("meeting_name", "")
                return func(meeting_name)
            else:
                return f"错误：未实现的函数调用 - {function_name}"

        except Exception as e:
            logger.error(f"执行函数时出错: {str(e)}")
            return f"执行函数时出错: {str(e)}"

    def invoke(self, inputs: dict) -> dict:
        """调用代理处理用户输入"""
        try:
            user_query = inputs.get("input", "")

            # 创建函数调用提示词
            prompt = self._create_function_calling_prompt(user_query)
            logger.info(f"生成的提示词: {prompt}")

            # 调用LLM
            response = self.llm.invoke(prompt)

            # 获取响应内容
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)

            logger.info(f"LLM响应: {response_text}")

            # 解析函数调用
            function_call = self._parse_function_call(response_text)
            if not function_call:
                return {"output": f"错误：无法解析函数调用 - {response_text}"}

            logger.info(f"解析的函数调用: {function_call}")

            # 执行函数
            result = self._execute_function(function_call)

            return {"output": result}

        except Exception as e:
            logger.error(f"代理调用失败: {str(e)}")
            return {"output": f"处理查询时出错: {str(e)}"}

# 初始化LLM
def initialize_llm(config):
    """
    根据配置初始化LLM模型
    """
    llm_type = config.get("LLM", "LLM_TYPE", fallback="ollama")  # 获取LLM类型
    model_name = config.get("LLM", "MODEL", fallback="deepseek-r1:32b")  # 获取模型名称
    
    # 根据配置选择LLM类型
    if llm_type.lower() == "openai":
        # 使用OpenAI API
        openai_api_key = config.get("LLM", "OPENAI_API_KEY", fallback="")
        openai_api_base = config.get("LLM", "OPENAI_API_BASE", fallback="https://api.openai.com/v1")
        
        print(f"使用OpenAI模型: {model_name}, API基础URL: {openai_api_base}")
        llm = ChatOpenAI(
            model_name=model_name,
            openai_api_key=openai_api_key,
            openai_api_base=openai_api_base,
        )
    else:
        # 默认使用Ollama
        ollama_base_url = config.get("LLM", "OLLAMA_BASE_URL", fallback="http://localhost:11434")
        
        print(f"使用Ollama模型: {model_name}, 基础URL: {ollama_base_url}")
        llm = ChatOllama(
            model=model_name, 
            base_url=ollama_base_url
        )
    
    return llm

# 创建代理
def create_agent():
    """
    创建函数调用代理
    """
    try:
        # 加载配置
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')
        
        # 初始化LLM
        llm = initialize_llm(config)
        
        # 添加输出过滤回调
        output_filter = OutputFilterCallbackHandler()
        if not hasattr(llm, 'callbacks') or llm.callbacks is None:
            llm.callbacks = [output_filter]
        else:
            llm.callbacks.append(output_filter)
        
        # 导入需要的工具模块
        from langchain.tools import Tool
        
        # 定义工具
        query_event_surround_resource_tool = Tool(
            name="query_event_surround_resource",
            description="查询事件周边的应急资源,输入格式: 事件名称,距离,资源类型(可选：队伍/仓库/专家/避难场所/医疗卫生/防护目标/企业信息/物资/装备/应急资源/危险源),例如：西兴街道火灾事件,5,救援队伍",
            func=query_event_surround_resource
        )

        preview_camera_tool = Tool(
            name="preview_camera",
            description="预览摄像头,输入格式: 摄像头ID,例如：camera001",
            func=preview_camera
        )

        one_map_position_resource_tool = Tool(
            name="one_map_position_resource",
            description="一张图查看并定位资源,输入格式: 资源名称,资源类型(为空),例如：防汛抗旱物资储备库,仓库",
            func=one_map_position_resource
        )

        event_stop_plan_tool = Tool(
            name="event_stop_plan",
            description="终止事件预案,输入格式: 事件名称 例如: 西兴街道火灾事件",
            func=event_stop_plan
        )

        event_start_plan_tool = Tool(
            name="event_start_plan",
            description="启动事件预案,输入格式: 事件名称,预案名称（为空）,例如: 西兴街道火灾事件",
            func=event_start_plan
        )

        query_all_event_infos_tool = Tool(
            name="query_all_event_infos",
            description="搜索事件.输入格式: 页码(默认1,类型是int),每页大小(默认50,类型是int),事件状态(可选,默认为空),事件名称(可选,当为空时查所有事件),事件等级(可选,默认为空)。例如: 1,50,4,洪涝灾害事件,1",
            func=query_all_event_infos
        )

        search_emergency_plan_tool = Tool(
            name="search_emergency_plan",
            description="搜索应急预案,输入格式: 预案类型（为空时查所有预案）,页码(默认为1),页面大小(默认20),例如: 森林火灾,1,20",
            func=search_emergency_plan
        )
        
        # 新增工具定义
        query_area_plans_tool = Tool(
            name="query_area_plans",
            description="根据区域名称查询预案信息,输入格式: 区域名称（市/区）,例如: 杭州市",
            func=query_area_plans
        )
        
        query_resource_cameras_tool = Tool(
            name="query_resource_cameras",
            description="查询资源周边的视频信息,输入格式: 资源名称,资源类型,例如: 防汛物资仓库,repository",
            func=query_resource_cameras
        )
        
        start_call_by_name_tool = Tool(
            name="start_call_by_name",
            description="根据队伍/人名发起指定的呼叫,输入格式: 资源名称,资源类型,呼叫类型,例如: 李四,person,0 (视频通话)",
            func=start_call_by_name
        )
        
        query_model_resources_tool = Tool(
            name="query_model_resources",
            description="按照事件模型名称搜索附近应急资源,输入格式: 模型名称,例如: 森林火灾模型",
            func=query_model_resources
        )
        
        start_real_time_travel_tool = Tool(
            name="start_real_time_travel",
            description="启动一键调度跟进某某事件所有救援队伍实时轨迹，输入格式：事件名称（可选），例如暴雨事件",
            func=start_real_time_travel
        )
        
        start_meeting_tool = Tool(
            name="start_meeting",
            description="开启会议,输入格式: 会议名称,例如: 应急指挥会议",
            func=start_meeting
        )
        
        end_meeting_tool = Tool(
            name="end_meeting",
            description="结束会议,输入格式: 会议名称,例如: 应急指挥会议",
            func=end_meeting
        )
        
        # 加载工具
        tools = [
            query_event_surround_resource_tool,
            preview_camera_tool,
            one_map_position_resource_tool,
            event_stop_plan_tool,
            event_start_plan_tool,
            query_all_event_infos_tool,
            search_emergency_plan_tool,
            query_area_plans_tool,
            query_resource_cameras_tool,
            start_call_by_name_tool,
            query_model_resources_tool,
            start_real_time_travel_tool,
            start_meeting_tool,
            end_meeting_tool
        ]
        
        # 使用自定义提示词模板创建代理
        react_prompt = PromptTemplate.from_template(REACT_CUSTOM_PROMPT)
        
        # 使用create_react_agent替代initialize_agent
        agent = create_react_agent(
            llm=llm, 
            tools=tools, 
            prompt=react_prompt
        )
        
        # 创建代理执行器
        agent_executor = AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=tools,
            verbose=True,
            max_iterations=5,
            handle_parsing_errors=True,
            callbacks=[output_filter],  # 添加输出过滤器
            return_intermediate_steps=False  # 设置为False，只返回最终结果，不返回中间步骤
        )
        
        return agent_executor
    except Exception as e:
        logger.error(f"创建代理失败: {str(e)}")
        return None

# 格式化返回结果
def format_agent_response(response):
    """
    格式化代理的响应
    """
    try:
        # 检查是否为空
        if not response:
            return response

        #检查是否因迭代限制停止，但先检查是否有有效数据
        if "Agent stopped due to iteration limit" in str(response):
            return "没有找到相应工具或资源，请确认任务名称，并重新输入。"
            
        # 快速路径：如果响应中包含已格式化的资源列表，直接返回
        if "公里范围内的" in response and "：\n" in response:
            # 提取格式化后的资源列表部分
            parts = response.split("：\n")
            if len(parts) > 1:
                return response
        
        # 快速路径：检测启动预案相关的返回结果
        if "已成功启动事件" in response or "成功启动预案" in response or "已成功终止事件" in response or "成功终止预案" in response:
            logger.info("检测到预案启动/终止结果，直接返回")
            return response
        
        # 快速路径：检测成功定位资源的返回结果
        if "成功定位" in response and "【" in response and "】" in response:
            logger.info("检测到资源定位结果，直接返回")
            return response
            
        # 快速路径：检测常见的空结果返回
        if "未找到符合条件的事件" in response or "未找到符合条件的应急预案" in response or "未找到符合条件的" in response:
            logger.info("检测到空结果返回，直接返回")
            return response
        
        # 快速路径：如果是API直接返回的Observation结果
        observation_match = re.search(r'Observation:(.*?)(?:Action:|Final Answer:|$)', response, re.DOTALL)
        if observation_match:
            observation = observation_match.group(1).strip()
            if "公里范围内的" in observation:
                return observation
        
        # 如果有Final Answer且包含格式化内容，直接返回
        final_answer_match = re.search(r'Final Answer:(.*?)$', response, re.DOTALL)
        if final_answer_match:
            final_answer = final_answer_match.group(1).strip()
            if "公里范围内的" in final_answer or "- **" in final_answer:
                return final_answer
        
        # 移除预定义前缀
        prefixes_to_remove = ["Observation:", "Thought:", "Action:", "Action Input:", "Final Answer:"]
        lines = response.strip().split('\n')
        filtered_lines = []
        
        for line in lines:
            if not any(line.strip().startswith(prefix) for prefix in prefixes_to_remove):
                filtered_lines.append(line)
        
        return '\n'.join(filtered_lines).strip()

    except Exception as e:
        logger.error(f"格式化响应错误: {str(e)}")
        return response


def create_function_calling_agent():
    """
    创建Function Calling模式的本地大模型代理
    """
    try:
        # 加载配置
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')

        # 初始化LLM
        llm = initialize_llm(config)

        # 添加输出过滤回调
        output_filter = OutputFilterCallbackHandler()
        if not hasattr(llm, 'callbacks') or llm.callbacks is None:
            llm.callbacks = [output_filter]
        else:
            llm.callbacks.append(output_filter)

        # 定义函数列表（参考jiuan_agent的格式）
        functions = [
            {
                "name": "query_event_surround_resource",
                "description": "查询事件周边的应急资源。resource_type为可选参数，可以是：队伍、仓库、专家、避难场所、医疗卫生、防护目标、企业信息、物资、装备、医院、危险源、视频，或者留空查询所有类型",
                "parameters": {
                    "event_name": "string",
                    "distance": "int",
                    "resource_type": "string|optional"
                }
            },
            {
                "name": "preview_camera",
                "parameters": {
                    "camera_id": "string"
                }
            },
            {
                "name": "one_map_position_resource",
                "parameters": {
                    "resource_name": "string"
                }
            },
            {
                "name": "event_stop_plan",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "event_start_plan",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "query_all_event_infos",
                "parameters": {
                    "page": 1,
                    "page_size": 50
                }
            },
            {
                "name": "search_emergency_plan",
                "parameters": {
                    "page": 1,
                    "page_size": 20
                }
            },
            {
                "name": "query_area_plans",
                "parameters": {
                    "area_name": "string"
                }
            },
            {
                "name": "query_resource_cameras",
                "parameters": {
                    "resource_name": "string",
                    "resource_type": "string"
                }
            },
            {
                "name": "start_call_by_name",
                "description": "根据队伍/人名发起指定的呼叫;resource_type: rescue_team, person;calling_type:0(视频通话),1(电话)",
                "parameters": {
                    "resource_name": "string",
                    "resource_type": "string",
                    "calling_type": "int"
                }
            },
            {
                "name": "query_model_resources",
                "parameters": {
                    "model_name": "string"
                }
            },
            {
                "name": "start_real_time_travel",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "start_meeting",
                "parameters": {
                    "meeting_name": "string"
                }
            },
            {
                "name": "end_meeting",
                "parameters": {
                    "meeting_name": "string"
                }
            }
        ]

        # 创建自定义的Function Calling代理执行器
        return CustomFunctionCallingAgent(llm, functions, output_filter)
    except Exception as e:
        logger.error(f"创建Function Calling代理失败: {str(e)}")
        return None