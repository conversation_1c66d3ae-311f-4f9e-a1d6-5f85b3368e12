# API函数调用方法配置说明

## 概述

本系统现在支持两种API函数调用方法：
1. **ReAct模式** - 使用ReAct（Reasoning and Acting）模式的本地大模型代理
2. **Function Calling模式** - 使用Function Calling模式的本地大模型代理

## 配置方法

在 `config.ini` 文件的 `[API]` 部分添加以下配置项：

```ini
[API]
# 函数调用方法选择: react 或 functioncalling
# react: 使用ReAct模式的本地大模型代理
# functioncalling: 使用Function Calling模式的本地大模型代理
FUNCTION_CALLING_METHOD = react
```

### 配置选项

- `react` (默认): 使用ReAct模式，适合需要推理步骤的复杂查询
- `functioncalling`: 使用Function Calling模式，适合直接的函数调用场景

## 工作流程

1. 用户发送查询到 `/run_workflow/` 端点
2. 系统调用 `search_local_information_stream` 方法
3. 如果检测到API函数调用请求，调用 `function_calling_agent` 方法
4. `function_calling_agent` 首先尝试通过正则表达式等方法直接提取参数
5. 如果无法直接提取参数，根据配置选择代理类型：
   - `react`: 调用 `create_agent()` 创建ReAct代理
   - `functioncalling`: 调用 `create_function_calling_agent()` 创建Function Calling代理

## 技术实现

### 修改的文件

1. **config.ini** - 添加了 `FUNCTION_CALLING_METHOD` 配置项
2. **src/agent/agent_core.py** - 添加了 `create_function_calling_agent()` 方法
3. **src/agent/agent_service.py** - 修改了代理选择逻辑
4. **src/agent/__init__.py** - 导出新的函数

### 代理差异

#### ReAct模式
- 使用 `create_react_agent` 
- 基于推理和行动的循环模式
- 适合复杂的多步骤推理

#### Function Calling模式  
- 使用 `create_openai_functions_agent`
- 直接的函数调用模式
- 适合明确的API调用场景

## 使用建议

- **默认使用ReAct模式**: 对于大多数应急管理查询，ReAct模式提供更好的推理能力
- **Function Calling模式**: 当需要更直接的函数调用时使用，可能在某些场景下响应更快

## 注意事项

1. 两种模式使用相同的工具集和API函数
2. 配置更改后无需重启服务，会在下次查询时生效
3. 如果配置项不存在或值无效，系统默认使用ReAct模式
4. 两种模式都使用本地部署的大模型（ollama或openai接口）

## 故障排除

如果遇到问题：
1. 检查 `config.ini` 中的配置项是否正确
2. 确认大模型服务正常运行
3. 查看日志中的代理创建信息
4. 可以通过修改配置在两种模式间切换进行对比测试
