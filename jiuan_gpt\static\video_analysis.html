<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频分析 - 应急管理大模型平台</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        header {
            background-color: #1e3a8a;
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .nav-menu {
            display: flex;
        }
        
        .nav-menu a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-menu a:hover {
            background-color: #2d4eaf;
        }
        
        .nav-menu a.active {
            background-color: #2d4eaf;
        }
        
        main {
            flex: 1;
            padding: 20px 0;
        }
        
        .page-title {
            margin-bottom: 20px;
            color: #1e3a8a;
        }
        
        .analysis-container {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .upload-section {
            flex: 1;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            padding: 20px;
        }
        
        .result-section {
            flex: 1;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        
        .drag-area {
            border: 2px dashed #1e3a8a;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: background-color 0.3s;
            cursor: pointer;
        }
        
        .drag-area.active {
            background-color: #f0f5ff;
        }
        
        .drag-area .icon {
            font-size: 50px;
            color: #1e3a8a;
            margin-bottom: 15px;
        }
        
        .drag-area h3 {
            margin: 0 0 10px;
            color: #1e3a8a;
        }
        
        .drag-area p {
            margin: 0;
            color: #666;
        }
        
        .file-info {
            display: none;
            margin-top: 20px;
            text-align: left;
            padding: 10px;
            background-color: #f0f5ff;
            border-radius: 4px;
        }
        
        .video-preview {
            max-width: 100%;
            margin-top: 10px;
            border-radius: 4px;
        }
        
        .query-input {
            display: flex;
            margin-top: 20px;
        }
        
        .query-input input {
            flex: 1;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            font-size: 14px;
        }
        
        .query-input input:focus {
            outline: none;
            border-color: #1e3a8a;
        }
        
        button {
            padding: 10px 20px;
            background-color: #1e3a8a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: bold;
        }
        
        button:hover {
            background-color: #2d4eaf;
        }
        
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .query-input button {
            margin-left: 10px;
        }
        
        .analysis-title {
            margin-top: 0;
            color: #1e3a8a;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .analysis-content {
            flex: 1;
            overflow-y: auto;
            padding: 10px 0;
            line-height: 1.6;
        }
        
        .loading {
            text-align: center;
            padding: 30px;
            color: #666;
        }
        
        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid rgba(30, 58, 138, 0.2);
            border-radius: 50%;
            border-top-color: #1e3a8a;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            padding: 10px;
            background-color: #fee2e2;
            color: #b91c1c;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success-result {
            padding: 15px;
            background-color: #f0f9ff;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .result-header {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .result-header h3 {
            margin: 0;
            color: #1e3a8a;
            font-size: 1.2em;
        }
        
        .result-header small {
            color: #666;
            font-size: 0.8em;
        }
        
        .result-content {
            line-height: 1.6;
            color: #333;
        }
        
        .timeout-message {
            padding: 15px;
            background-color: #fef3c7;
            color: #92400e;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }
        
        .timeout-message h3 {
            margin: 0 0 10px;
            color: #92400e;
        }
        
        .timeout-message p {
            margin: 5px 0;
        }
        
        .query-btn {
            margin-top: 15px;
            padding: 8px 16px;
            background-color: #1e3a8a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
        }
        
        .query-btn:hover {
            background-color: #2d4eaf;
        }
        
        #progress-info {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8fafc;
            border-radius: 4px;
            font-size: 0.9em;
            color: #666;
        }
        
        #progress-info p {
            margin: 5px 0;
        }
        
        #progress-info small {
            color: #999;
            font-size: 0.8em;
        }
        
        .note {
            background-color: #f0f5ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 0.9em;
            color: #666;
        }
        
        footer {
            text-align: center;
            padding: 20px;
            background-color: #1e3a8a;
            color: #ccc;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .analysis-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-content">
            <div class="logo">应急管理大模型平台</div>
            <div class="nav-menu">
                <a href="/">首页</a>
                <a href="/chat">智能对话</a>
                <a href="/emergency_qa">应急知识问答</a>
                <a href="/image_analysis">图片分析</a>
                <a href="/video_analysis" class="active">视频分析</a>
            </div>
        </div>
    </header>
    
    <main class="container">
        <h1 class="page-title">视频分析</h1>
        
        <div class="analysis-container">
            <section class="upload-section">
                <div id="drag-area" class="drag-area">
                    <div class="icon">🎬</div>
                    <h3>拖放视频文件到这里</h3>
                    <p>或者 <strong>点击浏览</strong></p>
                    <input type="file" id="file-input" hidden accept=".mp4,.avi,.mkv,.mov,.wmv,.flv">
                </div>
                
                <div id="file-info" class="file-info">
                    <div>
                        <strong>已选择文件:</strong> <span id="file-name"></span>
                    </div>
                    <div>
                        <strong>文件大小:</strong> <span id="file-size"></span>
                    </div>
                    <video id="video-preview" class="video-preview" controls></video>
                </div>
                
                <div class="note">
                    <strong>注意:</strong> 请上传小于10MB的视频文件，支持MP4、AVI、MKV、MOV、WMV、FLV格式。文件过大可能导致上传失败。
                </div>
                
                <div class="query-input">
                    <input type="text" id="query-input" placeholder="请输入您要提问的内容，例如：分析这段视频中发生了什么事件？">
                    <button id="analyze-btn" disabled>分析视频</button>
                </div>
            </section>
            
            <section class="result-section">
                <h2 class="analysis-title">分析结果</h2>
                <div id="analysis-content" class="analysis-content">
                    <p>请上传视频并提问，系统将对视频内容进行分析。</p>
                </div>
            </section>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>© 2024 应急管理大模型平台 版权所有</p>
        </div>
    </footer>
    
    <script>
        // 获取DOM元素
        const dragArea = document.getElementById('drag-area');
        const fileInput = document.getElementById('file-input');
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        const videoPreview = document.getElementById('video-preview');
        const queryInput = document.getElementById('query-input');
        const analyzeBtn = document.getElementById('analyze-btn');
        const analysisContent = document.getElementById('analysis-content');
        
        // 当前选择的文件
        let selectedFile = null;
        
        // 点击拖放区域触发文件选择
        dragArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // 监听文件选择变化
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                handleFile(this.files[0]);
            }
        });
        
        // 拖放事件监听
        ['dragover', 'dragleave', 'drop'].forEach(eventName => {
            dragArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        // 拖放视觉反馈
        dragArea.addEventListener('dragover', function() {
            this.classList.add('active');
        });
        
        dragArea.addEventListener('dragleave', function() {
            this.classList.remove('active');
        });
        
        // 处理拖放的文件
        dragArea.addEventListener('drop', function(e) {
            this.classList.remove('active');
            
            if (e.dataTransfer.files.length > 0) {
                handleFile(e.dataTransfer.files[0]);
            }
        });
        
        // 处理选择的文件
        function handleFile(file) {
            // 检查是否是视频文件，并验证支持的格式
            const supportedFormats = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv'];
            const fileExtension = file.name.split('.').pop().toLowerCase();
            
            if (!file.type.match('video.*') || !supportedFormats.includes(fileExtension)) {
                alert('请选择支持的视频文件格式！\n支持格式：MP4、AVI、MKV、MOV、WMV、FLV');
                return;
            }
            
            // 检查文件大小
            if (file.size > 10 * 1024 * 1024) { // 10MB
                alert('文件大小超过10MB，请选择较小的视频文件');
                return;
            }
            
            selectedFile = file;
            
            // 显示文件信息
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';
            
            // 显示视频预览
            const videoURL = URL.createObjectURL(file);
            videoPreview.src = videoURL;
            
            // 启用分析按钮
            analyzeBtn.disabled = false;
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 分析按钮点击事件
        analyzeBtn.addEventListener('click', analyzeVideo);
        
        // 当前任务ID
        let currentTaskId = null;
        
        // 分析视频
        function analyzeVideo() {
            if (!selectedFile) {
                alert('请先选择一个视频文件！');
                return;
            }
            
            // 准备要发送的数据
            const formData = new FormData();
            formData.append('video', selectedFile);
            formData.append('query', queryInput.value || '请分析这段视频');
            
            // 禁用分析按钮
            analyzeBtn.disabled = true;
            
            // 显示加载中
            analysisContent.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在上传视频并开始分析...</p>
                    <div id="progress-info"></div>
                </div>
            `;
            
            // 选择使用哪种模式：完整流程（自动轮询）或分步模式
            // 这里使用完整流程模式，如果需要更精细控制可以切换到分步模式
            analyzeVideoComplete(formData);
        }
        
        // 完整流程分析（自动轮询）
        function analyzeVideoComplete(formData) {
            fetch('/analyze_video/', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                handleAnalysisResult(data);
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            })
            .finally(() => {
                analyzeBtn.disabled = false;
            });
        }
        
        // 分步模式分析（手动控制）
        function analyzeVideoStepwise(formData) {
            // 第一步：上传视频
            fetch('/upload_video/', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.task_id) {
                    currentTaskId = data.task_id;
                    updateProgress('视频上传成功，开始分析中...', data.task_id);
                    
                    // 开始轮询查询结果
                    pollTaskStatus(data.task_id);
                } else {
                    showError(data.message || '视频上传失败');
                    analyzeBtn.disabled = false;
                }
            })
            .catch(error => {
                showError('上传错误: ' + error.message);
                analyzeBtn.disabled = false;
            });
        }
        
        // 轮询任务状态
        function pollTaskStatus(taskId, maxAttempts = 60) {
            let attempts = 0;
            
            const poll = () => {
                attempts++;
                
                if (attempts > maxAttempts) {
                    showError('分析超时，请稍后手动查询结果');
                    analyzeBtn.disabled = false;
                    return;
                }
                
                fetch('/video_task_status/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ task_id: taskId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.data) {
                        // 检查分析是否完成
                        if (data.data.status === 'completed' || data.data.result) {
                            handleAnalysisResult(data);
                            analyzeBtn.disabled = false;
                        } else if (data.data.status === 'failed') {
                            showError('视频分析失败: ' + (data.data.message || '未知错误'));
                            analyzeBtn.disabled = false;
                        } else {
                            // 继续轮询
                            updateProgress(`正在分析中... (${attempts}/${maxAttempts})`, taskId);
                            setTimeout(poll, 5000); // 5秒后再次查询
                        }
                    } else {
                        showError('查询分析状态失败: ' + (data.message || '未知错误'));
                        analyzeBtn.disabled = false;
                    }
                })
                .catch(error => {
                    showError('查询错误: ' + error.message);
                    analyzeBtn.disabled = false;
                });
            };
            
            // 开始第一次查询
            setTimeout(poll, 2000); // 2秒后开始查询
        }
        
        // 处理分析结果
        function handleAnalysisResult(data) {
            if (data.status === 'success' && data.data) {
                // 提取分析结果
                let analysisResult = '';
                
                // 根据API响应结构提取结果
                if (data.data.result) {
                    analysisResult = data.data.result;
                } else if (data.data.msg) {
                    analysisResult = data.data.msg;
                } else if (data.data.response) {
                    analysisResult = data.data.response;
                } else {
                    analysisResult = '分析完成，但未能提取结果内容。';
                }
                
                // 计算处理时间
                const processingTime = data.elapsed_time ? 
                    `（处理时间：${Math.floor(data.elapsed_time / 60)}分${Math.floor(data.elapsed_time % 60)}秒）` : '';
                
                // 更新分析结果内容
                analysisContent.innerHTML = `
                    <div class="success-result">
                        <div class="result-header">
                            <h3>✅ 分析结果 ${processingTime}</h3>
                            ${data.task_id ? `<small>任务ID: ${data.task_id}</small>` : ''}
                        </div>
                        <div class="result-content">${analysisResult.replace(/\n/g, '<br>')}</div>
                    </div>
                `;
            } else if (data.status === 'timeout') {
                // 处理超时情况
                const timeoutMinutes = Math.floor((data.elapsed_time || 600) / 60);
                analysisContent.innerHTML = `
                    <div class="timeout-message">
                        <h3>⏰ 分析超时</h3>
                        <p>视频分析已运行${timeoutMinutes}分钟，可能需要更长时间处理。</p>
                        <p>系统已在后台继续处理，您可以：</p>
                        <ul>
                            <li>稍后使用任务ID查询结果</li>
                            <li>尝试上传更小的视频文件</li>
                            <li>检查视频格式是否为推荐格式（MP4）</li>
                        </ul>
                        ${data.task_id ? `
                            <p><strong>任务ID：${data.task_id}</strong></p>
                            <button onclick="queryTaskResult('${data.task_id}')" class="query-btn">立即查询结果</button>
                            <button onclick="copyTaskId('${data.task_id}')" class="query-btn" style="margin-left: 10px;">复制任务ID</button>
                        ` : ''}
                    </div>
                `;
            } else if (data.status === 'error') {
                // 处理错误情况
                const errorType = getErrorType(data.message);
                analysisContent.innerHTML = `
                    <div class="error-message">
                        <h3>❌ ${errorType.title}</h3>
                        <p><strong>错误详情：</strong> ${data.message}</p>
                        <div class="error-suggestions">
                            <p><strong>建议解决方案：</strong></p>
                            <ul>
                                ${errorType.suggestions.map(s => `<li>${s}</li>`).join('')}
                            </ul>
                        </div>
                        ${data.task_id ? `<p><small>任务ID: ${data.task_id}</small></p>` : ''}
                    </div>
                `;
            } else {
                // 显示错误信息
                showError(data.message || '分析过程中发生未知错误');
            }
        }
        
        // 获取错误类型和建议
        function getErrorType(message) {
            const msg = message.toLowerCase();
            
            if (msg.includes('格式') || msg.includes('format')) {
                return {
                    title: '文件格式错误',
                    suggestions: [
                        '请确保上传的是视频文件',
                        '支持格式：MP4、AVI、MKV、MOV、WMV、FLV',
                        '推荐使用MP4格式以获得最佳兼容性'
                    ]
                };
            } else if (msg.includes('大小') || msg.includes('10mb') || msg.includes('size')) {
                return {
                    title: '文件大小超限',
                    suggestions: [
                        '请确保视频文件小于10MB',
                        '可以使用视频压缩工具减小文件大小',
                        '建议上传时长较短的视频片段'
                    ]
                };
            } else if (msg.includes('网络') || msg.includes('连接') || msg.includes('timeout')) {
                return {
                    title: '网络连接问题',
                    suggestions: [
                        '请检查网络连接是否稳定',
                        '尝试重新上传文件',
                        '如果问题持续，请联系技术支持'
                    ]
                };
            } else if (msg.includes('查询') || msg.includes('状态')) {
                return {
                    title: '服务状态异常',
                    suggestions: [
                        '后台分析服务可能繁忙',
                        '请稍后再试',
                        '可以保存任务ID稍后查询'
                    ]
                };
            } else {
                return {
                    title: '未知错误',
                    suggestions: [
                        '请检查文件是否符合要求',
                        '尝试重新上传',
                        '如问题持续请联系技术支持'
                    ]
                };
            }
        }
        
        // 复制任务ID到剪贴板
        function copyTaskId(taskId) {
            navigator.clipboard.writeText(taskId).then(() => {
                alert('任务ID已复制到剪贴板');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = taskId;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('任务ID已复制到剪贴板');
            });
        }
        
        // 显示错误信息
        function showError(message) {
            analysisContent.innerHTML = `
                <div class="error-message">
                    <strong>错误:</strong> ${message}
                </div>
            `;
        }
        
        // 更新进度信息
        function updateProgress(message, taskId) {
            const progressInfo = document.getElementById('progress-info');
            if (progressInfo) {
                progressInfo.innerHTML = `
                    <p>${message}</p>
                    ${taskId ? `<small>任务ID: ${taskId}</small>` : ''}
                `;
            }
        }
        
        // 手动查询任务结果
        function queryTaskResult(taskId) {
            analysisContent.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在查询分析结果...</p>
                </div>
            `;
            
            fetch('/video_task_status/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ task_id: taskId })
            })
            .then(response => response.json())
            .then(data => {
                handleAnalysisResult(data);
            })
            .catch(error => {
                showError('查询错误: ' + error.message);
            });
        }
    </script>
</body>
</html> 