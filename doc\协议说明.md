# 1 协议说明

## 1.1 接口规则

| 传输方式     | 内部组件调用，采用HTTP、WEBSOCKET传输                                                                                                                |
| -------- | ---------------------------------------------------------------------------------------------------------------------------------------- |
| URL格式    | HTTP URL格式的一般形式为：https://${hostname}:${port}/${uri}, hostname：平台的IP地址或域名。port：平台的web访问端口。uri：API的uri，详见API的接口定义。举例：https://ip:port/xxxxx |
| 提交方式     | HTTP采用POST方法提交                                                                                                                           |
| 数据格式     | 提交数据见接口定义，返回数据都为JSON格式                                                                                                                   |
| 字符编码     | 文本数据统一采用UTF-8字符编码                                                                                                                        |
| 安全认证     | 内部组件调用，才有                                                                                                                                |
| 证书要求     | 暂不需要                                                                                                                                     |
| 调用成功判断逻辑 | 详见2.2【返回值规则】                                                                                                                             |

# 2 调用认证

基于共享秘钥的认证方式，详见。

## 2.1 敏感信息传输

求返回信息带有敏感信息的接口时，需要在调用接口前进行握手协商。适用于长连接模式的传输加密。密匙生成方式参见软件安全开发说明（java版）。

## 2.2 返回值规则

对于API接口调用返回的结果统一采用json格式，内容字段采用驼峰式方法命名,返回的接口内容格式如下：
{
    "code":"业务错误码",
    "msg":"返回的错误信息，方便对接和调试，不能有堆栈信息",
    "data":{数据}
}
code：业务错误码。调用成功时，返回code为“0”，当调用失败时，code为十六进制字符串错误码。查看每个API的错误码定义来判断错误类型。 msg：返回的错误信息，方便对接和调试，不能有堆栈信息 data：数据内容
返回的数据格式一般有如下几种： a) 只返回单条数据。示例1
{
    "code":"0",
    "msg":"返回的错误信息，方便对接和调试，不能有堆栈信息",
    "data":{
        "userId": xxxxxx,
        "name": "xxxxxx",
               …
    }
}
b) 返回多条数据。示例2
{
    "code":"0",
    "msg":"返回的错误信息，方便对接和调试，不能有堆栈信息",
    "data":{
        "total":5, //总条数
        "pageSize":20, //当前分页记录数
        "pageNo": 1,  //当前页
        "list": [{  //分页内容
            "userId": xxxxxx,
            "name": "xxxxxx",
                   …
            },{
            "userId": xxxxxx,
            "name": "xxxxxx",
                      …
        }]
    }
}
c) 没有分页信息的列表查询格式。示例3：
{
    "code":"0",
    "msg":"返回的错误信息，方便对接和调试，不能有堆栈信息",
    "data":{
    "total":5,
    "list": [{
        "userId": xxxxxx,
        "name": "xxxxxx",
                  …
        },{
        "userId": xxxxxx,
        "name": "xxxxxx",
                 …
            }]
     }
}
d) 若为图片数据，则直接返回二进制数据流。
e) 若状态码不为0，表示异常，则返回错误信息。示例4：
{
        "code ":"0x0190020f",             //业务错误码
        "msg":"返回的错误信息，方便对接和调试，不能有堆栈信息",
        "data":""               //预留字段，可以为空
}

****

# 3 接口说明

## 3.1 文件转写

####接口说明
接口用于提交音频文件，转写完成后返回转写内容。
音频文件需要满足要求：16位，16000采样率，原始pcm音频数据
接口返回耗时取决于音频文件长度，每一秒音频转写约需要一秒
####接口版本
V1.0.0
####请求上下文
/xmessagemas-cms
####接口地址
/api/v1/asr/file/transfer
####请求方法
POST
####数据提交方式
multipart/form-data
####请求Body定义
| 参数名称 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
|uploadFile | File | true | 音频文件 |

####示例
| 请求body: ----------------------------389776631559816967883795 Content-Disposition: form-data; name="uploadFile"; filename="250509-153133log_153008_to_153133_null.pcm" Content-Type: application/octet-stream xxx ----------------------------389776631559816967883795-- |
| --- |

####返回值
| 参数名称 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
|code | String | true | 请求结果码,返回 0 表示业务处理正常 |
|msg | String | true | 接口调用的结果说明 |
|data | Object | true | 接口返回参数详细说明 |
|   total | Numbe | true | 列表总数 |
|   list | Array | true | 列表 |
|     list[0] | String | true | 转写内容 |

####返回值举例
| {     "code": "0",     "msg": "SUCCESS",     "data": {         "total": 6,         "list": [                 "喂喂喂喂",                 "喂喂喂喂",                 "喂喂喂喂喂",                 "我以为我以为",                 "一二三四五六七八",                 "一二三四五六七八九"         ]     } } |
| --- |

## 3.2 结束任务

####接口说明
接口用于结束转写任务。结合4.1、4.2实时转写使用
调用后服务端会停止接收ws音频流，并继续转写未完成的音频，在转写完成后，主动关闭与客户端的连接
####接口版本
V1.0.0
####请求上下文
/xmessagemas-cms
####接口地址
/api/v1/asr/end
####请求方法
POST
####数据提交方式
application/json
####请求Body定义
| 参数名称 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
|     taskId | String | true | 实时转写ws连接建立时返回的任务id |

####示例
| {
    "taskId": "xxx" 
} |
| --- |

####返回值
| 参数名称 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
|     code | String | true | 请求结果码,返回 0 表示业务处理正常 |
|     msg | String | true | 接口调用的结果说明 |
|     data | Object | true | 接口返回参数详细说明 |

####返回值举例
| {     "code": "0",     "msg": "SUCCESS",     "data": true } |
| --- |

# 4 协议说明

## 4.1 实时转写（单路）

####接口说明
接口用于实时传输单路音频流，异步返回转写内容
####协议版本
V1.0.0
####请求上下文
/xmessagemas-cms
####协议类型
WS、WSS
####协议地址
/ws/audio/stream
####连接参数
| 参数名称 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
|     creator | String | false | 创建人，若前端直连，会默认使用单点登录用户作为创建人 |
|     speaker | String | false | 说话人，若前端直连，会默认使用单点登录用户作为说话人 |
|     taskId | String | false | 任务id，建立连接时返回，异常断开重连时，可携带此参数恢复任务（默认一条连接为一个任务） |

####请求示例
| ws:/ip:port/xmessagemas-cms/ws/audio/stream?creator=admin&speaker=admin |
| --- |

####返回值
| 参数名称 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
|     code | String | true | 请求结果码,返回 0 表示业务处理正常 |
|     msg | String | true | 接口调用的结果说明 |
|     data | Object | true | 接口返回参数详细说明 |
|         seq | Numbe | true | 消息顺序，从1开始，每次返回+1 |
|         type | String | true | 目前类型有：<br>NOTIFY（通知消息）、<br>TEXT（文本消息）、<br>CLOSE（关闭消息） |
|         taskId | String | true | 当前任务ID，type为NOTIFY、CLOSE时会返 |
|         textSeq | Numbe | true | 文本顺序，type为TEXT时会返<br>每新的一句+1，相同则代表当前句未结束，需要更新该句。 |
|         text | String | true | 该句转写文本，type为TEXT时会返<br>textSeq相同，则text属于同一句 |
|         speaker | String | true | 该句说话人，type为TEXT时会返<br>单路转写固定返回传入的说话人 |
|         status | String | true | 句子状态，type为TEXT时会返<br>CONTINUE（句子未结束）、<br>FINAL（句子结束，该用户下一条为新语句）、<br>COMPLETE（句子结束，同时该用户已说话结束（暂时结束，关掉麦克风或断连等，后续可能还会加入说话）） |

返回值举例
详见附A流程

## 4.2 实时转写（会议模式）

接口说明
接口用于通过一个连接，发送多路音频流进行转写，不同音频流之间使用说话人作区分
需使用自定义协议（见附D），将pcm信息（说话人等）和pcm数据封装在一起进行传输
协议版本
V1.0.0
请求上下文
/xmessagemas-cms
协议类型
WS、WSS
协议地址
/ws/audio/streams/protocol
连接参数
| 参数名称 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
|     creator | String | true | 创建人  |
|     taskId | String | false | 任务id，建立连接时返回，异常断开重连时，可携带此参数恢复任务（默认一条连接为一个任务） |

请求示例
| ws:/ip:port/xmessagemas-cms/ws/audio/streams/protocol?creator=admin |
| --- |

返回值
| 参数名称 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
|     code | String | true | 请求结果码,返回 0 表示业务处理正常 |
|     msg | String | true | 接口调用的结果说明 |
|     data | Object | true | 接口返回参数详细说明 |
|         seq | Numbe | true | 消息顺序，从1开始，每次返回+1 |
|         type | String | true | 目前类型有：<br>NOTIFY（通知消息）、<br>TEXT（文本消息）、<br>CLOSE（关闭消息） |
|         taskId | String | true | 当前任务ID，type为NOTIFY、CLOSE时会返 |
|         textSeq | Numbe | true | 文本顺序，type为TEXT时会返<br>每新的一句+1，相同则代表当前句未结束，需要更新该句。<br>textSeq在一个任务里唯一，但不一定顺序返回，可能B用户textSeq-2说完了，新建立了textSeq-3，A用户textSeq-1未结束 |
|         text | String | true | 该句转写文本，type为TEXT时会返<br>textSeq相同，则text属于同一句 |
|         speake | String | true | 该句说话人，type为TEXT时会返 |
|         status | String | true | 句子状态，type为TEXT时会返<br>CONTINUE（句子未结束）、<br>FINAL（句子结束，该用户下一条为新语句）、<br>COMPLETE（句子结束，同时该用户已说话结束（暂时结束，关掉麦克风或断连等，后续可能还会加入说话）） |

返回值举例
详见附A流程
自定义协议封装参数
| 参数名称 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
|     speaker | String | true | 该节音频数据说话人，同一说话人音频会使用单独通道进行转写。并在返回转写内容时携带 |

# 附A 实时转写交互流程示例

注：服务端返回消息均通过websocket text返回，使用utf8编码
1、客户端建立连接请求：
| ws:/ip:port/xmessagemas-cms/ws/audio/stream?creator=admin&speaker=admin |
| --- |

2、服务端返回任务创建成功信息：
| {     "code": "0",     "msg": "成功。",     "data": {         "seq": 1         "type": "NOTIFY",         "taskId": "69b2ae59a1fa4516a60ca9dc5c204f32"      } } |
| --- |

3、客户端向服务端发送pcm音频流：
3.1单路模式
| //持续发送websocket binary数据，推荐发送间隔为400ms，含12800字节 |
| --- |

3.2 会议模式
协议封装格式：4字节json长度+json+4字节pcm长度+pcm，示例见附D
示例：

传入的speaker会在转写消息中携带
4、服务端向客户端返回转写成功消息：
| //返回第二条 {     "code": "0",     "msg": "成功。",     "data": {         "seq": 2,         "type": "TEXT",         "textSeq": 1,          "text": "你好",         "speaker": "admin",         "status": "CONTINUE" //句子未完成      } } //返回第三条 {     "code": "0",     "msg": "成功。",     "data": {         "seq": 3,         "type": "TEXT",         "textSeq": 1,          "text": "你好，我是",         "speaker": "admin",         "status": "CONTINUE"     } } //返回第四条（会议模式示例）， {     "code": "0",     "msg": "成功。",     "data": {         "seq": 4,         "type": "TEXT",         "textSeq": 2,  //第二个说话人，使用了textSeq-2，textSeq-1尚未结束         "text": "我是",         "speaker": "zhangsan", // 新的说话人         "status": "CONTINUE"     } }  //返回第五条 {     "code": "0",     "msg": "成功。",     "data": {         "seq": 5,         "type": "TEXT",         "textSeq": 1,          "text": "你好，我是xx",         "speaker": "admin",         "status": "FINAL" //该句已结束，下一句为新句     } } //返回第六条 {     "code": "0",     "msg": "成功。",     "data": {         "seq": 6,         "type": "TEXT",         "textSeq": 3, //该句为新句，textSeq+1         "text": "请问",         "speaker": "admin",         "status": "CONTINUE" //该句未结束     } } …… //返回第N条 {     "code": "0",     "msg": "成功。",     "data": {         "seq": 100,         "type": "TEXT",         "textSeq": 10, //该句为新句，textSeq+1         "text": "",         "speaker": "admin",         "status": "COMPLETE" //该用户暂时无新音频流进来     } } |
| --- |

5、（可选）客户端可直接断掉ws连接，服务端保留任务30秒后，会清除内存中任务，30秒内，客户端可携带任务id重连，使用该方式结束任务，可能会丢失部分转写内容
6、（可选）客户端可对应3.2接口结束任务，调用后服务端会停止接收ws音频流，并继续转写未完成的音频，在转写完成后，服务端会发送通知，并主动关闭与客户端的连接
7、任务关闭通知：
| {     "code": "0",     "msg": "成功。",     "data": {         "seq": 101         "type": "CLOSE",         "taskId": "69b2ae59a1fa4516a60ca9dc5c204f32"      } } |
| --- |

# 附B 前端采集pcm数据示例

| // 获取麦克风权限 this.mediaStream = await navigator.mediaDevices.getUserMedia({   audio: {     sampleRate: 16000, // 指定16kHz采样率     channelCount: 1, // 单声道     sampleSize: 16 // 16位   } })  // 创建音频上下文 this.audioContext = new (window.AudioContext || window.webkitAudioContext)({   sampleRate: 16000 }) const source = this.audioContext.createMediaStreamSource(this.mediaStream)  // 创建音频处理器 this.processor = this.audioContext.createScriptProcessor(1024, 1, 1)  this.processor.onaudioprocess = e => {   if (this.isConnected && this.socket.readyState === WebSocket.OPEN) {     // 获取PCM数据并发送     const pcmData = this.floatTo16BitPCM(e.inputBuffer.getChannelData(0))     this.socket.send(pcmData)   } }  source.connect(this.processor) this.processor.connect(this.audioContext.destination) |
| --- |

# 附C 前端处理转写数据示例

| if (body.type === 'TEXT') {   // 转写结果   let currentSegment = this.transcriptSegments.find(     item => item.textSeq === body.textSeq   )   if (currentSegment) {     // 更新旧句子     currentSegment.text = body.text   } else {     // 新句子     currentSegment = {       textSeq: body.textSeq,       text: body.text,       speaker: body.speaker     }     this.transcriptSegments.push(currentSegment)     this.$nextTick(() => {       const container = this.$refs.asrScrollWrapRef       if (container) {         // 使用平滑滚动         container.scrollTo({           top: container.scrollHeight,           behavior: 'smooth'         })       }     })   }   // 触发视图更新   this.$forceUpdate() } |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |

# 附D 会议模式自定义协议构建（java示例）

| /**      * 构建自定义二进制消息      * @param jsonStr JSON字符串      * @param pcmData PCM字节数组      * @return 完整协议字节数组      */     public static byte[] buildRequest(String jsonStr, byte[] pcmData) {         if (null == jsonStr) {             jsonStr = "";         }         byte[] jsonData = jsonStr.getBytes(StandardCharsets.UTF_8);         // 序列化JSON数据部分         byte[] jsonBytes = ByteBuffer.allocate(4 + jsonData.length)                 .order(ByteOrder.BIG_ENDIAN) //大端序                 .putInt(jsonData.length) //写入4字节长度                 .put(jsonData) //写入json数据                 .array();         // 序列化PCM数据部分         byte[] pcmBytes = ByteBuffer.allocate(4 + pcmData.length)                 .order(ByteOrder.BIG_ENDIAN) //大端序                 .putInt(pcmData.length) //写入4字节长度                 .put(pcmData) //写入pcm数据                 .array();         // 合并数据         byte[] binaryBytes = new byte[jsonBytes.length + pcmBytes.length];         System.arraycopy(jsonBytes, 0, binaryBytes, 0, jsonBytes.length);         System.arraycopy(pcmBytes, 0, binaryBytes, jsonBytes.length, pcmBytes.length);          return binaryBytes;     } |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
