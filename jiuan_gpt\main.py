from fastapi import FastAPI, Request, UploadFile, File, Form, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
import configparser
import os
import uuid
import logging
import traceback
import uvicorn
from typing import Optional
import sys

# 导入必要的模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from src.chat_api import chat_with_model
from src.image_api import analyze_image
from src.video_api import analyze_video
from src.emergency_qa_api import emergency_qa_query

# 创建 FastAPI 应用
app = FastAPI()

# 配置CORS
origins = ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("jiuan_gpt")

# 加载配置文件
config = configparser.ConfigParser()
try:
    config.read("jiuan_gpt/config.ini", encoding='utf-8')
    logger.info("配置文件加载成功")
except Exception as e:
    logger.error(f"配置文件加载失败: {str(e)}")

# 创建临时目录
temp_dir = "jiuan_gpt/temp_files"
if not os.path.exists(temp_dir):
    os.makedirs(temp_dir)
    logger.info(f"创建临时文件目录: {temp_dir}")

# 根路由
@app.get("/")
async def read_index():
    return FileResponse("jiuan_gpt/static/index.html")

# 聊天路由 - DeepSeek调用
@app.post("/chat/")
async def chat(request: Request):
    """与大模型对话"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")
        logger.info(f"聊天接口被调用: session_id={session_id}")

        # 解析请求体
        data = await request.json()
        user_message = data.get("message", "")
        conversation_id = data.get("conversation_id", None)  # 获取conversation_id
        
        if not user_message:
            return JSONResponse(
                content={"status": "error", "message": "消息不能为空"}
            )

        # 调用大模型接口，传递conversation_id
        response = await chat_with_model(user_message, config, logger, conversation_id)
        
        # 返回结果
        return JSONResponse(content=response)
    
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理聊天请求时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理请求时出错，请联系管理员查看日志"}
        )

# 图片分析路由
@app.post("/analyze_image/")
async def analyze_image_api(
    request: Request, 
    image: UploadFile = File(...),
    query: Optional[str] = Form(None)
):
    """分析图片内容"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")
        
        # 如果没有提供query，设置默认值
        if not query:
            query = "请描述这张图片"
            
        logger.info(f"图片分析接口被调用: session_id={session_id}, query={query}")
        logger.info(f"上传的图片: 文件名={image.filename}, 内容类型={image.content_type}")

        # 保存临时文件
        temp_dir = "jiuan_gpt/temp_files"
        file_extension = os.path.splitext(image.filename)[1] if image.filename and '.' in image.filename else '.jpg'
        unique_filename = f"{str(uuid.uuid4())}{file_extension}"
        file_path = os.path.join(temp_dir, unique_filename)
        
        # 确保临时目录存在
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        
        # 读取并保存图片内容
        await image.seek(0)
        content = await image.read()
        if not content:
            logger.error("上传的图片内容为空")
            return JSONResponse(
                content={"status": "error", "message": "上传的图片内容为空"}
            )
            
        logger.info(f"读取到图片内容，大小: {len(content)} 字节")
        
        with open(file_path, "wb") as buffer:
            buffer.write(content)
            
        logger.info(f"图片已保存到: {file_path}")
        
        # 调用图片分析接口，传递空的history
        response = await analyze_image(file_path, query, config, logger, history=[])
        
        # 清理临时文件
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"临时图片文件已删除: {file_path}")
        except Exception as e:
            logger.warning(f"删除临时文件失败: {str(e)}")
        
        # 返回结果
        return JSONResponse(content=response)
    
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理图片分析请求时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理请求时出错，请联系管理员查看日志"}
        )

# 视频分析路由
@app.post("/analyze_video/")
async def analyze_video_api(
    request: Request, 
    video: UploadFile = File(...),
    query: Optional[str] = Form(None)
):
    """分析视频内容"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")
        
        # 如果没有提供query，设置默认值
        if not query:
            query = "请分析这段视频"
            
        logger.info(f"视频分析接口被调用: session_id={session_id}, query={query}")
        logger.info(f"上传的视频: 文件名={video.filename}, 内容类型={video.content_type}")

        # 验证文件格式
        if video.filename:
            file_extension = video.filename.split('.')[-1].lower()
            supported_formats = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv']
            if file_extension not in supported_formats:
                logger.error(f"不支持的视频格式: {file_extension}")
                return JSONResponse(
                    content={
                        "status": "error", 
                        "message": f"不支持的视频格式。支持格式：{', '.join(supported_formats).upper()}"
                    }
                )

        # 保存临时文件
        temp_dir = "jiuan_gpt/temp_files"
        file_extension = os.path.splitext(video.filename)[1] if video.filename and '.' in video.filename else '.mp4'
        unique_filename = f"{str(uuid.uuid4())}{file_extension}"
        file_path = os.path.join(temp_dir, unique_filename)
        
        # 确保临时目录存在
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        
        # 读取并保存视频内容
        await video.seek(0)
        content = await video.read()
        if not content:
            logger.error("上传的视频内容为空")
            return JSONResponse(
                content={"status": "error", "message": "上传的视频内容为空"}
            )
        
        # 验证文件大小（10MB限制）
        if len(content) > 10 * 1024 * 1024:  # 10MB
            logger.error(f"视频文件过大: {len(content)} 字节")
            return JSONResponse(
                content={
                    "status": "error", 
                    "message": "视频文件大小超过10MB限制，请上传较小的文件"
                }
            )
            
        logger.info(f"读取到视频内容，大小: {len(content)} 字节")
        
        with open(file_path, "wb") as buffer:
            buffer.write(content)
            
        logger.info(f"视频已保存到: {file_path}")
        
        # 调用视频分析接口（异步轮询版本）
        response = await analyze_video(file_path, query, config, logger)
        
        # 清理临时文件
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"临时视频文件已删除: {file_path}")
        except Exception as e:
            logger.warning(f"删除临时文件失败: {str(e)}")
        
        # 返回结果
        return JSONResponse(content=response)
    
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理视频分析请求时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理请求时出错，请联系管理员查看日志"}
        )

# 视频分析任务状态查询路由
@app.post("/video_task_status/")
async def video_task_status(request: Request):
    """查询视频分析任务状态"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")
        logger.info(f"视频任务状态查询接口被调用: session_id={session_id}")

        # 解析请求体
        data = await request.json()
        task_id = data.get("task_id", "")
        
        if not task_id:
            return JSONResponse(
                content={"status": "error", "message": "任务ID不能为空"}
            )

        # 调用视频结果查询接口
        from src.video_api import get_video_analysis_result
        response = await get_video_analysis_result(task_id, config, logger)
        
        # 返回结果
        return JSONResponse(content=response)
    
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理视频任务状态查询请求时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理请求时出错，请联系管理员查看日志"}
        )

# 视频分析上传接口（仅上传，不等待结果）
@app.post("/upload_video/")
async def upload_video_api(
    request: Request, 
    video: UploadFile = File(...),
    query: Optional[str] = Form(None)
):
    """上传视频进行分析，返回任务ID"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")
        
        # 如果没有提供query，设置默认值
        if not query:
            query = "请分析这段视频"
            
        logger.info(f"视频上传接口被调用: session_id={session_id}, query={query}")
        logger.info(f"上传的视频: 文件名={video.filename}, 内容类型={video.content_type}")

        # 验证文件格式
        if video.filename:
            file_extension = video.filename.split('.')[-1].lower()
            supported_formats = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv']
            if file_extension not in supported_formats:
                logger.error(f"不支持的视频格式: {file_extension}")
                return JSONResponse(
                    content={
                        "status": "error", 
                        "message": f"不支持的视频格式。支持格式：{', '.join(supported_formats).upper()}"
                    }
                )

        # 保存临时文件
        temp_dir = "jiuan_gpt/temp_files"
        file_extension = os.path.splitext(video.filename)[1] if video.filename and '.' in video.filename else '.mp4'
        unique_filename = f"{str(uuid.uuid4())}{file_extension}"
        file_path = os.path.join(temp_dir, unique_filename)
        
        # 确保临时目录存在
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        
        # 读取并保存视频内容
        await video.seek(0)
        content = await video.read()
        if not content:
            logger.error("上传的视频内容为空")
            return JSONResponse(
                content={"status": "error", "message": "上传的视频内容为空"}
            )
        
        # 验证文件大小（10MB限制）
        if len(content) > 10 * 1024 * 1024:  # 10MB
            logger.error(f"视频文件过大: {len(content)} 字节")
            return JSONResponse(
                content={
                    "status": "error", 
                    "message": "视频文件大小超过10MB限制，请上传较小的文件"
                }
            )
            
        logger.info(f"读取到视频内容，大小: {len(content)} 字节")
        
        with open(file_path, "wb") as buffer:
            buffer.write(content)
            
        logger.info(f"视频已保存到: {file_path}")
        
        # 调用视频上传接口
        from src.video_api import upload_video_for_analysis
        response = await upload_video_for_analysis(file_path, query, config, logger)
        
        # 清理临时文件
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"临时视频文件已删除: {file_path}")
        except Exception as e:
            logger.warning(f"删除临时文件失败: {str(e)}")
        
        # 返回结果
        return JSONResponse(content=response)
    
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理视频上传请求时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理请求时出错，请联系管理员查看日志"}
        )

# 查看图片分析页面
@app.get("/image_analysis")
async def image_analysis_page():
    return FileResponse("jiuan_gpt/static/image_analysis.html")

# 查看视频分析页面
@app.get("/video_analysis")
async def video_analysis_page():
    return FileResponse("jiuan_gpt/static/video_analysis.html")

# 查看聊天页面
@app.get("/chat")
async def chat_page():
    return FileResponse("jiuan_gpt/static/chat.html")

# 应急行业知识问答路由
@app.post("/emergency_qa/")
async def emergency_qa_api(request: Request):
    """应急行业知识问答"""
    try:
        # 获取会话ID
        session_id = request.headers.get("session-id", "default")
        logger.info(f"应急知识问答接口被调用: session_id={session_id}")

        # 解析请求体
        data = await request.json()
        query = data.get("query", "")
        conversation_id = data.get("conversation_id", None)
        
        if not query:
            return JSONResponse(
                content={"status": "error", "message": "问题不能为空"}
            )

        # 调用应急知识问答接口
        response = await emergency_qa_query(query, config, logger, conversation_id)
        
        # 返回结果
        return JSONResponse(content=response)
    
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理应急知识问答请求时发生异常:\n{error_trace}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理请求时出错，请联系管理员查看日志"}
        )

# 查看应急知识问答页面
@app.get("/emergency_qa")
async def emergency_qa_page():
    return FileResponse("jiuan_gpt/static/emergency_qa.html")

# 启动应用
if __name__ == "__main__":
    #   pip install fastapi uvicorn httpx python-multipart
    #   uvicorn jiuan_gpt.main:app --host *********** --port 8088
    uvicorn.run(app, host="***********", port=8088) 