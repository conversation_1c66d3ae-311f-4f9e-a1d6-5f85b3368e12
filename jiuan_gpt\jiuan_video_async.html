<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>久安大模型 - 异步视频分析</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .upload-section, .query-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .upload-section h3, .query-section h3 {
            margin-top: 0;
            color: #333;
            font-size: 1.3em;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        input[type="file"], input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        input[type="file"]:focus, input[type="text"]:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .task-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .task-id {
            font-family: monospace;
            background: #f0f0f0;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            margin: 5px 0;
        }
        .polling-status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>久安大模型</h1>
            <p>WebSocket视频分析系统</p>
        </div>
        
        <div class="content">
            <!-- 视频上传部分 -->
            <div class="upload-section">
                <h3>📹 WebSocket视频分析</h3>
                <div class="form-group">
                    <label for="videoFile">选择视频文件：</label>
                    <input type="file" id="videoFile" accept="video/*" required>
                </div>
                <div class="form-group">
                    <label for="videoQuery">分析问题：</label>
                    <textarea id="videoQuery" placeholder="请输入您想要分析的问题，例如：请分析这段视频的内容"></textarea>
                </div>
                <button class="btn" onclick="analyzeVideoWebSocket()" id="analyzeBtn">开始WebSocket分析</button>
                <button class="btn" onclick="uploadVideo()" style="background: #6c757d;">传统异步分析</button>
                
                <div class="loading" id="uploadLoading">
                    <div class="spinner"></div>
                    <p id="uploadLoadingText">正在分析视频...</p>
                </div>
                
                <div id="uploadResult"></div>
            </div>

            <!-- 结果查询部分 -->
            <div class="query-section">
                <h3>🔍 分析结果查询</h3>
                <div class="form-group">
                    <label for="taskId">任务ID：</label>
                    <input type="text" id="taskId" placeholder="请输入视频分析任务ID">
                </div>
                <button class="btn" onclick="queryResult()">查询结果</button>
                <button class="btn" onclick="startPolling()">开始轮询</button>
                <button class="btn" onclick="stopPolling()">停止轮询</button>
                
                <div class="loading" id="queryLoading">
                    <div class="spinner"></div>
                    <p>正在查询结果...</p>
                </div>
                
                <div id="queryResult"></div>
            </div>
        </div>
    </div>

    <script>
        let pollingInterval = null;
        let currentTaskId = null;

        // WebSocket视频分析
        async function analyzeVideoWebSocket() {
            const fileInput = document.getElementById('videoFile');
            const queryInput = document.getElementById('videoQuery');
            const loadingDiv = document.getElementById('uploadLoading');
            const resultDiv = document.getElementById('uploadResult');
            const loadingText = document.getElementById('uploadLoadingText');
            const analyzeBtn = document.getElementById('analyzeBtn');

            if (!fileInput.files[0]) {
                showResult(resultDiv, '请选择视频文件', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('video', fileInput.files[0]);
            formData.append('query', queryInput.value || '请分析这段视频的内容');

            loadingDiv.style.display = 'block';
            loadingText.textContent = '正在通过WebSocket分析视频...';
            resultDiv.innerHTML = '';
            analyzeBtn.disabled = true;

            try {
                const response = await fetch('/jiuan_analyze_video/', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'session-id': 'websocket_video_session'
                    }
                });

                const data = await response.json();
                loadingDiv.style.display = 'none';
                analyzeBtn.disabled = false;

                if (data.status === 'success') {
                    currentTaskId = data.task_id;
                    if (currentTaskId) {
                        document.getElementById('taskId').value = currentTaskId;
                    }
                    
                    let resultText = `✅ WebSocket视频分析完成！\n\n`;
                    if (data.task_id) {
                        resultText += `📋 任务ID: ${data.task_id}\n`;
                    }
                    if (data.data && data.data.elapsed_time) {
                        resultText += `⏱️ 分析耗时: ${data.data.elapsed_time.toFixed(2)} 秒\n\n`;
                    }
                    
                    // 显示分析结果
                    if (data.data && data.data.result) {
                        resultText += `📊 分析结果:\n${JSON.stringify(data.data.result, null, 2)}`;
                    } else {
                        resultText += `📊 详细信息:\n${JSON.stringify(data.data, null, 2)}`;
                    }
                    
                    showResult(resultDiv, resultText, 'success');
                    
                    // 显示任务信息
                    if (data.task_id) {
                        const taskInfo = document.createElement('div');
                        taskInfo.className = 'task-info';
                        taskInfo.innerHTML = `
                            <strong>🎯 WebSocket分析完成</strong><br>
                            任务ID: <span class="task-id">${data.task_id}</span><br>
                            <small>分析已通过WebSocket完成，无需轮询查询</small>
                        `;
                        resultDiv.appendChild(taskInfo);
                    }
                } else {
                    showResult(resultDiv, `❌ WebSocket分析失败: ${data.message}`, 'error');
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                analyzeBtn.disabled = false;
                showResult(resultDiv, `❌ WebSocket请求失败: ${error.message}`, 'error');
            }
        }

        // 上传视频（传统方式）
        async function uploadVideo() {
            const fileInput = document.getElementById('videoFile');
            const queryInput = document.getElementById('videoQuery');
            const historyInput = document.getElementById('videoHistory');
            const loadingDiv = document.getElementById('uploadLoading');
            const resultDiv = document.getElementById('uploadResult');

            if (!fileInput.files[0]) {
                showResult(resultDiv, '请选择视频文件', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('video', fileInput.files[0]);
            formData.append('query', queryInput.value || '请分析这段视频');
            
            if (historyInput.value.trim()) {
                formData.append('history', historyInput.value.trim());
            }

            loadingDiv.style.display = 'block';
            resultDiv.innerHTML = '';

            try {
                const response = await fetch('/jiuan_upload_video/', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'session-id': 'video_upload_session'
                    }
                });

                const data = await response.json();
                loadingDiv.style.display = 'none';

                if (data.status === 'success') {
                    currentTaskId = data.task_id;
                    document.getElementById('taskId').value = currentTaskId;
                    
                    let resultText = `✅ 视频上传成功！\n\n`;
                    resultText += `📋 任务ID: ${data.task_id}\n`;
                    resultText += `💬 消息: ${data.message}\n\n`;
                    resultText += `📊 详细信息:\n${JSON.stringify(data.data, null, 2)}`;
                    
                    showResult(resultDiv, resultText, 'success');
                    
                    // 显示任务信息
                    const taskInfo = document.createElement('div');
                    taskInfo.className = 'task-info';
                    taskInfo.innerHTML = `
                        <strong>🎯 任务已创建</strong><br>
                        任务ID: <span class="task-id">${data.task_id}</span><br>
                        <small>您可以使用此任务ID查询分析结果</small>
                    `;
                    resultDiv.appendChild(taskInfo);
                } else {
                    showResult(resultDiv, `❌ 上传失败: ${data.message}`, 'error');
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                showResult(resultDiv, `❌ 请求失败: ${error.message}`, 'error');
            }
        }

        // 查询结果
        async function queryResult() {
            const taskIdInput = document.getElementById('taskId');
            const loadingDiv = document.getElementById('queryLoading');
            const resultDiv = document.getElementById('queryResult');

            if (!taskIdInput.value.trim()) {
                showResult(resultDiv, '请输入任务ID', 'error');
                return;
            }

            loadingDiv.style.display = 'block';
            resultDiv.innerHTML = '';

            try {
                const response = await fetch('/jiuan_video_result/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'session-id': 'video_query_session'
                    },
                    body: JSON.stringify({
                        task_id: taskIdInput.value.trim()
                    })
                });

                const data = await response.json();
                loadingDiv.style.display = 'none';

                if (data.status === 'success') {
                    let resultText = `✅ 查询成功！\n\n`;
                    resultText += `📊 分析结果:\n${JSON.stringify(data.data, null, 2)}`;
                    showResult(resultDiv, resultText, 'success');
                } else {
                    showResult(resultDiv, `❌ 查询失败: ${data.message}`, 'error');
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                showResult(resultDiv, `❌ 请求失败: ${error.message}`, 'error');
            }
        }

        // 开始轮询
        function startPolling() {
            const taskIdInput = document.getElementById('taskId');
            const resultDiv = document.getElementById('queryResult');

            if (!taskIdInput.value.trim()) {
                showResult(resultDiv, '请输入任务ID', 'error');
                return;
            }

            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            const pollingStatus = document.createElement('div');
            pollingStatus.className = 'polling-status';
            pollingStatus.id = 'pollingStatus';
            pollingStatus.innerHTML = '🔄 开始轮询查询结果...';
            resultDiv.appendChild(pollingStatus);

            let pollCount = 0;
            pollingInterval = setInterval(async () => {
                pollCount++;
                pollingStatus.innerHTML = `🔄 轮询中... (第${pollCount}次查询)`;
                
                try {
                    const response = await fetch('/jiuan_video_result/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'session-id': 'video_polling_session'
                        },
                        body: JSON.stringify({
                            task_id: taskIdInput.value.trim()
                        })
                    });

                    const data = await response.json();
                    
                    if (data.status === 'success' && data.data) {
                        // 检查是否完成
                        const responseData = data.data;
                        if (responseData.status === 'completed' || 
                            responseData.status === 'success' || 
                            responseData.result || 
                            responseData.response || 
                            responseData.msg) {
                            
                            stopPolling();
                            pollingStatus.innerHTML = '✅ 分析完成！';
                            pollingStatus.style.background = '#d4edda';
                            pollingStatus.style.borderColor = '#c3e6cb';
                            
                            let resultText = `🎉 视频分析完成！\n\n`;
                            resultText += `📊 分析结果:\n${JSON.stringify(responseData, null, 2)}`;
                            showResult(resultDiv, resultText, 'success');
                        } else if (responseData.status === 'failed' || responseData.status === 'error') {
                            stopPolling();
                            pollingStatus.innerHTML = '❌ 分析失败';
                            pollingStatus.style.background = '#f8d7da';
                            pollingStatus.style.borderColor = '#f5c6cb';
                            showResult(resultDiv, `❌ 分析失败: ${JSON.stringify(responseData, null, 2)}`, 'error');
                        }
                    }
                } catch (error) {
                    pollingStatus.innerHTML = `⚠️ 查询出错: ${error.message}`;
                }
            }, 5000); // 每5秒查询一次
        }

        // 停止轮询
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
                
                const pollingStatus = document.getElementById('pollingStatus');
                if (pollingStatus && pollingStatus.innerHTML.includes('轮询中')) {
                    pollingStatus.innerHTML = '⏹️ 轮询已停止';
                    pollingStatus.style.background = '#f8f9fa';
                    pollingStatus.style.borderColor = '#dee2e6';
                }
            }
        }

        // 显示结果
        function showResult(container, message, type) {
            const existingResults = container.querySelectorAll('.result');
            existingResults.forEach(result => result.remove());
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }

        // 页面卸载时清理轮询
        window.addEventListener('beforeunload', stopPolling);
    </script>
</body>
</html>
