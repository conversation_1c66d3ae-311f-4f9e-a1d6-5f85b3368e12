import httpx
import json
import base64
import os
import asyncio
import time

async def upload_video_for_analysis(file_path, query, config, logger):
    """
    上传视频进行分析，返回任务ID
    
    Args:
        file_path: 视频文件路径
        query: 用户查询内容
        config: 配置对象
        logger: 日志对象
        
    Returns:
        dict: 包含任务ID的响应结果
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"视频文件不存在: {file_path}")
            return {"status": "error", "message": "视频文件不存在"}
            
        # 获取配置信息
        upload_url = config.get("API", "VIDEO_UPLOAD_URL")
        content_type = config.get("API", "VIDEO_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("API", "VIDEO_TIMEOUT", fallback=120)
        token = config.get("API", "TOKEN", fallback="")
        verify_ssl = config.getboolean("API", "VERIFY_SSL", fallback=False)
        
        logger.info(f"准备调用视频上传接口: {upload_url}")
        logger.info(f"视频文件: {file_path}, 查询: {query}")
        
        # 读取视频文件并转换为base64
        with open(file_path, "rb") as video_file:
            video_content = video_file.read()
            video_base64 = base64.b64encode(video_content).decode('utf-8')
            
        logger.info(f"视频转换为base64成功，长度: {len(video_base64)}")
        
        # 准备请求头
        headers = {
            "Content-Type": content_type,
            "Accept": "application/json"
        }
        
        # 添加认证头
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        # 准备请求体 - 根据视频分析接口格式
        payload = {
            "video": video_base64,
            "msg": query
        }
        
        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到视频上传接口")
            response = await client.post(
                upload_url, 
                json=payload, 
                headers=headers, 
                timeout=timeout
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"视频上传接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }
                
            # 解析响应内容
            response_data = response.json()
            logger.info(f"视频上传接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")
            
            # 提取任务ID
            task_id = None
            if response_data.get("data"):
                task_id = response_data["data"]
            elif response_data.get("task_id"):
                task_id = response_data["task_id"]
            elif response_data.get("id"):
                task_id = response_data["id"]
                
            if not task_id:
                logger.error("未能从响应中获取任务ID")
                return {
                    "status": "error",
                    "message": "上传成功但未获取到任务ID",
                    "data": response_data
                }
            
            # 返回结果
            return {
                "status": "success",
                "task_id": task_id,
                "message": "视频上传成功，正在分析中...",
                "data": response_data
            }
                
    except Exception as e:
        logger.error(f"调用视频上传接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用视频上传接口时出错: {str(e)}"
        }

async def get_video_analysis_result(task_id, config, logger):
    """
    根据任务ID获取视频分析结果
    
    Args:
        task_id: 视频分析任务ID
        config: 配置对象
        logger: 日志对象
        
    Returns:
        dict: 视频分析结果
    """
    try:
        # 获取配置信息
        result_url = config.get("API", "VIDEO_RESULT_URL")
        content_type = config.get("API", "VIDEO_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("API", "VIDEO_TIMEOUT", fallback=120)
        token = config.get("API", "TOKEN", fallback="")
        verify_ssl = config.getboolean("API", "VERIFY_SSL", fallback=False)
        
        logger.info(f"准备查询视频分析结果: {result_url}")
        logger.info(f"任务ID: {task_id}")
        
        # 准备请求头
        headers = {
            "Content-Type": content_type,
            "Accept": "application/json"
        }
        
        # 添加认证头
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        # 准备请求体 - 根据查询结果接口格式
        payload = {
            "task_id": task_id
        }
        
        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到视频结果查询接口")
            response = await client.post(
                result_url, 
                json=payload, 
                headers=headers, 
                timeout=timeout
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"视频结果查询接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }
                
            # 解析响应内容
            response_data = response.json()
            logger.info(f"视频结果查询接口返回: {json.dumps(response_data, ensure_ascii=False)[:100]}...")
            
            # 返回结果
            return {
                "status": "success",
                "data": response_data
            }
                
    except Exception as e:
        logger.error(f"查询视频分析结果时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"查询视频分析结果时出错: {str(e)}"
        }

async def analyze_video_with_polling(file_path, query, config, logger, max_wait_time=600, poll_interval=10):
    """
    分析视频内容（包含上传和轮询获取结果的完整流程）
    
    Args:
        file_path: 视频文件路径
        query: 用户查询内容
        config: 配置对象
        logger: 日志对象
        max_wait_time: 最大等待时间（秒，默认10分钟）
        poll_interval: 轮询间隔（秒，默认10秒）
        
    Returns:
        dict: 视频分析结果
    """
    try:
        # 第一步：上传视频获取任务ID
        upload_result = await upload_video_for_analysis(file_path, query, config, logger)
        if upload_result["status"] != "success":
            return upload_result
            
        task_id = upload_result["task_id"]
        logger.info(f"视频上传成功，任务ID: {task_id}")
        
        # 第二步：智能轮询获取分析结果
        start_time = time.time()
        consecutive_failures = 0
        max_consecutive_failures = 3
        
        while True:
            current_time = time.time()
            elapsed_time = current_time - start_time
            
            # 检查是否超时
            if elapsed_time > max_wait_time:
                logger.warning(f"视频分析超时，任务ID: {task_id}, 耗时: {elapsed_time:.2f}秒")
                return {
                    "status": "timeout",
                    "message": f"视频分析超时（{max_wait_time//60}分钟），任务可能仍在后台处理中",
                    "task_id": task_id,
                    "elapsed_time": elapsed_time
                }
            
            try:
                # 查询分析结果
                result = await get_video_analysis_result(task_id, config, logger)
                
                if result["status"] == "success" and result.get("data"):
                    consecutive_failures = 0  # 重置失败计数
                    
                    # 更智能的状态判断
                    data = result["data"]
                    
                    # 检查各种可能的完成状态
                    if (data.get("status") == "completed" or 
                        data.get("status") == "success" or
                        data.get("result") or 
                        data.get("response") or
                        data.get("msg")):
                        
                        logger.info(f"视频分析完成，任务ID: {task_id}, 耗时: {elapsed_time:.2f}秒")
                        return {
                            "status": "success",
                            "data": data,
                            "task_id": task_id,
                            "elapsed_time": elapsed_time
                        }
                    
                    # 检查失败状态
                    elif (data.get("status") == "failed" or 
                          data.get("status") == "error" or
                          data.get("error")):
                        
                        error_msg = data.get("message") or data.get("error") or "分析失败"
                        logger.error(f"视频分析失败，任务ID: {task_id}, 错误: {error_msg}")
                        return {
                            "status": "error",
                            "message": f"视频分析失败: {error_msg}",
                            "data": data,
                            "task_id": task_id,
                            "elapsed_time": elapsed_time
                        }
                    
                    # 仍在处理中
                    else:
                        progress_msg = data.get("progress") or f"分析进行中 ({elapsed_time//60:.0f}分{elapsed_time%60:.0f}秒)"
                        logger.info(f"视频分析进行中，任务ID: {task_id}, 状态: {data.get('status', 'processing')}")
                        
                else:
                    # 查询接口本身失败
                    consecutive_failures += 1
                    logger.warning(f"查询视频分析状态失败，任务ID: {task_id}, 连续失败次数: {consecutive_failures}")
                    
                    # 如果连续失败太多次，可能是接口问题
                    if consecutive_failures >= max_consecutive_failures:
                        logger.error(f"连续查询失败{max_consecutive_failures}次，可能是接口异常")
                        return {
                            "status": "error",
                            "message": "查询分析状态连续失败，可能是服务异常",
                            "task_id": task_id,
                            "elapsed_time": elapsed_time
                        }
            
            except Exception as query_error:
                consecutive_failures += 1
                logger.error(f"查询视频分析状态异常，任务ID: {task_id}, 错误: {str(query_error)}")
                
                if consecutive_failures >= max_consecutive_failures:
                    return {
                        "status": "error",
                        "message": f"查询分析状态异常: {str(query_error)}",
                        "task_id": task_id,
                        "elapsed_time": elapsed_time
                    }
            
            # 动态调整轮询间隔（越久轮询越慢）
            if elapsed_time < 60:  # 前1分钟，每5秒查询一次
                current_poll_interval = 5
            elif elapsed_time < 300:  # 前5分钟，每10秒查询一次
                current_poll_interval = 10
            else:  # 5分钟后，每30秒查询一次
                current_poll_interval = 30
            
            logger.info(f"等待{current_poll_interval}秒后再次查询，任务ID: {task_id}")
            await asyncio.sleep(current_poll_interval)
            
    except Exception as e:
        logger.error(f"视频分析流程出错: {str(e)}")
        return {
            "status": "error",
            "message": f"视频分析流程出错: {str(e)}"
        }

# 为了向后兼容，保留原来的函数名
async def analyze_video(file_path, query, config, logger):
    """
    分析视频内容（向后兼容的入口函数）
    """
    return await analyze_video_with_polling(file_path, query, config, logger) 