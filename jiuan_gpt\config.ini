[API]
# deepseek对话接口
CHAT_URL = https://*************/api/ai_apaas/conversation/runs
CHAT_CONTENT_TYPE = application/json
CHAT_TIMEOUT = 120

# 图片识别接口
IMAGE_URL = https://*************/dhxx
IMAGE_CONTENT_TYPE = application/json
IMAGE_TIMEOUT = 120

# 视频识别接口 - 上传分析
VIDEO_UPLOAD_URL = https://*************/api/analysis/video
VIDEO_CONTENT_TYPE = application/json
VIDEO_TIMEOUT = 120

# 视频识别接口 - 获取结果
VIDEO_RESULT_URL = https://*************/api/analysis/video/result
VIDEO_CONTENT_TYPE = application/json

# 应急行业知识问答接口
EMERGENCY_QA_URL = https://*************/rpc/2.0/ai_custom/v1/wenxinworkshop/plugin/xx
EMERGENCY_QA_CONTENT_TYPE = application/json
EMERGENCY_QA_TIMEOUT = 120

# 兼容性配置（为了保持向后兼容）
VIDEO_URL = https://*************/api/analysis/video

# 接口访问令牌
TOKEN = SElLIEV2WU5lZG9RRStvVUtTaTQ6Y2pvMEw5cVN5YzNMUi8zRFdrR3JtUVZ1a0NNczluWXhFemNhb3dXR01vUT06MTc0NDc4NjE4MTc3Mg==

# 是否验证SSL证书
VERIFY_SSL = false

[VIDEO_PROCESSING]
# 视频处理相关配置
MAX_FILE_SIZE_MB = 10
SUPPORTED_FORMATS = mp4,avi,mkv,mov,wmv,flv
MAX_WAIT_TIME_MINUTES = 10
INITIAL_POLL_INTERVAL_SECONDS = 5
LONG_POLL_INTERVAL_SECONDS = 30
MAX_CONSECUTIVE_FAILURES = 3

[RETRY]
# 重试机制配置
MAX_RETRIES = 3
RETRY_DELAY_SECONDS = 2
BACKOFF_MULTIPLIER = 2

[LOGGING]
# 日志配置
LOG_LEVEL = INFO
LOG_FORMAT = %(asctime)s - %(name)s - %(levelname)s - %(message)s
MAX_LOG_FILES = 7 