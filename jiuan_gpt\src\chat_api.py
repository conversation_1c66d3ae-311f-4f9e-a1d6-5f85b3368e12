import httpx
import json
import base64
import uuid

async def chat_with_model(user_message, config, logger, conversation_id=None):
    """
    与大模型进行对话
    
    Args:
        user_message: 用户输入的消息
        config: 配置对象
        logger: 日志对象
        conversation_id: 会话ID，如果不提供则生成新的
        
    Returns:
        dict: 大模型的响应结果
    """
    try:
        # 获取配置信息
        url = config.get("API", "CHAT_URL")
        content_type = config.get("API", "CHAT_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("API", "CHAT_TIMEOUT", fallback=120)
        token = config.get("API", "TOKEN", fallback="")
        verify_ssl = config.getboolean("API", "VERIFY_SSL", fallback=False)
        
        logger.info(f"准备调用对话接口: {url}")
        logger.info(f"用户消息: {user_message}")
        
        # 如果没有提供conversation_id，生成一个新的
        if not conversation_id:
            conversation_id = str(uuid.uuid4())
            
        # 准备请求头
        headers = {
            "Content-Type": content_type,
            "Accept": "application/json"
        }
        
        # 添加认证头
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        # 准备请求体 - 根据图片中的DeepSeek接口格式
        payload = {
            "query": user_message,
            "stream": False,
            "conversation_id": conversation_id
        }
        
        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到对话接口")
            response = await client.post(
                url, 
                json=payload, 
                headers=headers, 
                timeout=timeout
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"对话接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }
                
            # 解析响应内容
            response_data = response.json()
            logger.info(f"对话接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")
            
            # 返回结果，包含conversation_id以便后续使用
            return {
                "status": "success",
                "data": response_data,
                "conversation_id": conversation_id
            }
                
    except Exception as e:
        logger.error(f"调用对话接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用对话接口时出错: {str(e)}"
        } 