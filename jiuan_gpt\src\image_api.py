import httpx
import json
import base64
import os

async def analyze_image(file_path, query, config, logger, history=None):
    """
    分析图片内容
    
    Args:
        file_path: 图片文件路径
        query: 用户查询内容
        config: 配置对象
        logger: 日志对象
        history: 对话历史，默认为空列表
        
    Returns:
        dict: 图片分析结果
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"图片文件不存在: {file_path}")
            return {"status": "error", "message": "图片文件不存在"}
            
        # 获取配置信息
        url = config.get("API", "IMAGE_URL")
        content_type = config.get("API", "IMAGE_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("API", "IMAGE_TIMEOUT", fallback=120)
        token = config.get("API", "TOKEN", fallback="")
        verify_ssl = config.getboolean("API", "VERIFY_SSL", fallback=False)
        
        logger.info(f"准备调用图片分析接口: {url}")
        logger.info(f"图片文件: {file_path}, 查询: {query}")
        
        # 读取图片文件并转换为base64
        with open(file_path, "rb") as image_file:
            image_content = image_file.read()
            image_base64 = base64.b64encode(image_content).decode('utf-8')
            
        logger.info(f"图片转换为base64成功，长度: {len(image_base64)}")
        
        # 如果没有提供history，使用空列表
        if history is None:
            history = []
        
        # 准备请求头
        headers = {
            "Content-Type": content_type,
            "Accept": "application/json"
        }
        
        # 添加认证头
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        # 准备请求体 - 根据图片中的图片识别接口格式
        payload = {
            "text": query,
            "history": history,
            "image": image_base64
        }
        
        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到图片分析接口")
            response = await client.post(
                url, 
                json=payload, 
                headers=headers, 
                timeout=timeout
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"图片分析接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }
                
            # 解析响应内容
            response_data = response.json()
            logger.info(f"图片分析接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")
            
            # 返回结果
            return {
                "status": "success",
                "data": response_data
            }
                
    except Exception as e:
        logger.error(f"调用图片分析接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用图片分析接口时出错: {str(e)}"
        } 